#!/usr/bin/env python3
import base64

def check_best_xor():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    decoded = base64.b64decode(clean_data)
    
    # En iyi XOR anahtarı: 8183
    key = bytes.fromhex("8183")
    result = bytes([decoded[i] ^ key[i % len(key)] for i in range(len(decoded))])
    
    print("=== EN İYİ XOR SONUCU (8183) ===")
    print(f"Anahtar: {key.hex()}")
    
    # ASCII kontrolü
    ascii_count = sum(1 for b in result if 32 <= b <= 126)
    ascii_ratio = ascii_count / len(result)
    print(f"ASCII oranı: {ascii_ratio:.2%}")
    
    # İlk 200 karakteri göster
    try:
        text = result.decode('utf-8', errors='ignore')
        print(f"Metin: {text[:200]}...")
        
        # Tüm metni dosyaya kaydet
        with open("best_xor_result.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print("Dosyaya kaydedildi: best_xor_result.txt")
        
    except Exception as e:
        print(f"UTF-8 çözme hatası: {e}")
        
        # Binary olarak kaydet
        with open("best_xor_result.bin", "wb") as f:
            f.write(result)
        print("Binary olarak kaydedildi: best_xor_result.bin")
    
    # Hex dump
    print("\n=== HEX DUMP (İlk 128 byte) ===")
    for i in range(0, min(128, len(result)), 16):
        hex_part = " ".join(f"{b:02x}" for b in result[i:i+16])
        ascii_part = "".join(chr(b) if 32 <= b <= 126 else "." for b in result[i:i+16])
        print(f"{i:04x}: {hex_part:<48} {ascii_part}")

if __name__ == "__main__":
    check_best_xor()
