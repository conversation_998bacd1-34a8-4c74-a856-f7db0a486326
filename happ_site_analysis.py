#!/usr/bin/env python3
import requests
import base64
import json
from urllib.parse import urljoin

def analyze_happ_crypto_site():
    """Happ crypto sitesini analiz et"""
    
    base_url = "https://crypto.happ.su/"
    
    print("HAPP CRYPTO SİTE ANALİZİ")
    print("=" * 50)
    
    # Farklı User-Agent'lar dene
    headers_list = [
        {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        },
        {
            'User-Agent': 'curl/7.68.0'
        }
    ]
    
    for i, headers in enumerate(headers_list):
        print(f"\n--- User-Agent {i+1} ile deneme ---")
        try:
            response = requests.get(base_url, headers=headers, timeout=10)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("Site erişilebilir!")
                print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
                print(f"Content-Length: {len(response.text)}")
                
                # HTML içeriğini analiz et
                content = response.text.lower()
                
                # Anahtar kelimeler ara
                keywords = [
                    'decrypt', 'encrypt', 'rsa', 'private', 'public', 'key',
                    'happ://', 'crypt2', 'api', 'ajax', 'post', 'form',
                    'javascript', 'crypto', 'cipher'
                ]
                
                found_keywords = []
                for keyword in keywords:
                    if keyword in content:
                        found_keywords.append(keyword)
                
                print(f"Bulunan anahtar kelimeler: {found_keywords}")
                
                # JavaScript dosyalarını ara
                import re
                js_files = re.findall(r'src=["\']([^"\']*\.js[^"\']*)["\']', content)
                print(f"JavaScript dosyaları: {js_files}")
                
                # Form action'larını ara
                forms = re.findall(r'<form[^>]*action=["\']([^"\']*)["\'][^>]*>', content)
                print(f"Form action'ları: {forms}")
                
                # API endpoint'leri ara
                api_patterns = [
                    r'/api/[^"\'\s]*',
                    r'/decrypt[^"\'\s]*',
                    r'/encrypt[^"\'\s]*',
                    r'/crypto[^"\'\s]*'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"API endpoints ({pattern}): {matches}")
                
                # HTML'i dosyaya kaydet
                with open("happ_site_content.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print("Site içeriği kaydedildi: happ_site_content.html")
                
                return response.text
                
        except requests.exceptions.RequestException as e:
            print(f"Hata: {e}")
    
    return None

def try_direct_decrypt_api():
    """Direkt decrypt API'sini dene"""
    
    encrypted_code = "b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    base_url = "https://crypto.happ.su/"
    
    # Yaygın API endpoint'leri dene
    endpoints = [
        "decrypt",
        "api/decrypt", 
        "crypto/decrypt",
        "decode",
        "api/decode",
        "d",
        "dec"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    print("\n" + "=" * 50)
    print("DİREKT API DENEMELERİ")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = urljoin(base_url, endpoint)
        print(f"\nDenenen URL: {url}")
        
        # Farklı payload formatları dene
        payloads = [
            {"data": encrypted_code},
            {"encrypted": encrypted_code},
            {"code": encrypted_code},
            {"text": encrypted_code},
            {"cipher": encrypted_code},
            {"happ": encrypted_code},
            {"crypt2": encrypted_code}
        ]
        
        for payload in payloads:
            try:
                response = requests.post(url, json=payload, headers=headers, timeout=10)
                print(f"  Payload: {list(payload.keys())[0]} -> Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"  BAŞARILI! Response: {response.text[:200]}...")
                    
                    try:
                        json_response = response.json()
                        print(f"  JSON Response: {json_response}")
                        
                        # Çözülen metni kaydet
                        if 'result' in json_response:
                            with open("decrypted_result.txt", "w", encoding="utf-8") as f:
                                f.write(str(json_response['result']))
                            print("  Çözülen metin kaydedildi: decrypted_result.txt")
                            
                    except:
                        # JSON değilse text olarak kaydet
                        with open("decrypted_result.txt", "w", encoding="utf-8") as f:
                            f.write(response.text)
                        print("  Response text olarak kaydedildi: decrypted_result.txt")
                        
                elif response.status_code != 404:
                    print(f"  Response: {response.text[:100]}...")
                    
            except requests.exceptions.RequestException as e:
                print(f"  Hata: {e}")

def main():
    print("HAPP CRYPTO ŞİFRE ÇÖZME DENEMESİ")
    print("=" * 60)
    
    # Site analizi
    site_content = analyze_happ_crypto_site()
    
    # Direkt API denemeleri
    try_direct_decrypt_api()
    
    print("\n" + "=" * 60)
    print("ANALİZ TAMAMLANDI")
    print("Eğer başarılı olursa, decrypted_result.txt dosyası oluşturulacak.")
    print("Site içeriği happ_site_content.html dosyasında incelenebilir.")

if __name__ == "__main__":
    main()
