#!/usr/bin/env python3
import base64
import binascii
import os
import string

def try_base64_decode(encrypted_text):
    """Base64 çözme denemesi"""
    try:
        # URL'den sonraki kısmı al
        if "happ://crypt2/" in encrypted_text:
            encrypted_data = encrypted_text.split("happ://crypt2/")[1]
        else:
            encrypted_data = encrypted_text
        
        # Base64 çözme
        decoded = base64.b64decode(encrypted_data)
        print(f"Base64 çözüldü, uzunluk: {len(decoded)} bytes")
        
        # Metin olarak yazdırmayı dene
        try:
            text = decoded.decode('utf-8')
            print("UTF-8 olarak çözüldü:")
            print(text)
            return text
        except:
            print("UTF-8 çözülemedi, hex olarak:")
            print(decoded.hex())
            return decoded
            
    except Exception as e:
        print(f"Base64 çözme hatası: {e}")
        return None

def try_common_ciphers(data):
    """Yaygın şifreleme yöntemlerini dene"""
    
    # Caesar cipher denemesi
    if isinstance(data, str):
        print("\n=== <PERSON> Cipher Denemesi ===")
        for shift in range(1, 26):
            result = ""
            for char in data:
                if char.isalpha():
                    ascii_offset = 65 if char.isupper() else 97
                    result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
                else:
                    result += char
            if "turkmen" in result.lower() or "baglan" in result.lower():
                print(f"Caesar shift {shift}: {result}")
    
    # ROT13 denemesi
    if isinstance(data, str):
        print("\n=== ROT13 Denemesi ===")
        rot13 = data.encode().decode('rot13') if hasattr(data.encode(), 'decode') else None
        if rot13:
            print(f"ROT13: {rot13}")

def analyze_encrypted_text(encrypted_text):
    """Şifreli metni analiz et"""
    print("=== ŞİFRELİ METİN ANALİZİ ===")
    print(f"Toplam uzunluk: {len(encrypted_text)}")
    print(f"İlk 50 karakter: {encrypted_text[:50]}")
    print(f"Son 50 karakter: {encrypted_text[-50:]}")
    
    # Protokol kontrolü
    if encrypted_text.startswith("happ://crypt2/"):
        print("Özel protokol tespit edildi: happ://crypt2/")
        return encrypted_text.split("happ://crypt2/")[1]
    
    return encrypted_text

def main():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    print("ŞİFRE ÇÖZME BAŞLADI...")
    
    # Analiz
    clean_data = analyze_encrypted_text(encrypted_text)
    
    # Base64 çözme denemesi
    decoded_data = try_base64_decode(encrypted_text)
    
    if decoded_data:
        # Yaygın şifreleri dene
        try_common_ciphers(decoded_data)
    
    print("\n=== SONUÇ ===")
    if decoded_data:
        print("Veri başarıyla çözüldü!")
        
        # Dosyaya kaydet
        if isinstance(decoded_data, str):
            with open("decrypted_text.txt", "w", encoding="utf-8") as f:
                f.write(decoded_data)
        else:
            with open("decrypted_data.bin", "wb") as f:
                f.write(decoded_data)
        
        print("Çözülen veri dosyaya kaydedildi.")
    else:
        print("Şifre çözülemedi.")

if __name__ == "__main__":
    main()
