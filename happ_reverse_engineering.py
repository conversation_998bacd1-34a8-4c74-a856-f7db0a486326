#!/usr/bin/env python3
import base64
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import struct

def try_happ_specific_decryption():
    """Happ VPN'e özel şifreleme yöntemlerini dene"""
    
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("HAPP VPN TERSINE MÜHENDİSLİK")
    print("=" * 50)
    print(f"Şifreli veri uzunluğu: {len(encrypted_data)} bytes")
    
    # Happ VPN'e özel anahtar kombinasyonları
    happ_keys = [
        # Domain tabanlı
        "crypto.happ.su",
        "happ.su", 
        "crypto",
        "happ",
        
        # Protokol tabanlı
        "crypt2",
        "happ://crypt2/",
        "happcrypt2",
        
        # VPN tabanlı
        "happvpn",
        "happ_vpn",
        "HAPPVPN",
        
        # Yaygın şifreler
        "password",
        "secret",
        "key",
        "admin",
        "default",
        "123456",
        "happ123",
        "crypto123"
    ]
    
    best_results = []
    
    for key_str in happ_keys:
        print(f"\nAnahtar deneniyor: {key_str}")
        
        # Farklı hash yöntemleri
        key_variants = [
            key_str.encode('utf-8'),
            hashlib.md5(key_str.encode()).digest(),
            hashlib.sha1(key_str.encode()).digest(),
            hashlib.sha256(key_str.encode()).digest(),
            hashlib.sha512(key_str.encode()).digest(),
            # Kısaltılmış versiyonlar
            hashlib.md5(key_str.encode()).digest()[:16],
            hashlib.sha1(key_str.encode()).digest()[:16],
            hashlib.sha256(key_str.encode()).digest()[:16],
            hashlib.sha256(key_str.encode()).digest()[:32],
        ]
        
        for i, key in enumerate(key_variants):
            try:
                # XOR çözme
                result = bytes([encrypted_data[j] ^ key[j % len(key)] for j in range(len(encrypted_data))])
                
                # Kalite kontrolü
                ascii_count = sum(1 for b in result if 32 <= b <= 126)
                ascii_ratio = ascii_count / len(result)
                
                if ascii_ratio > 0.4:  # %40'dan fazla ASCII
                    try:
                        text = result.decode('utf-8', errors='ignore')
                        
                        # VPN config anahtar kelimeleri
                        vpn_score = 0
                        vpn_keywords = [
                            'client', 'remote', 'port', 'proto', 'dev',
                            'ca', 'cert', 'key', 'tls-auth', 'cipher',
                            'auth', 'comp-lzo', 'verb', 'mute',
                            'resolv-retry', 'nobind', 'persist-key',
                            'persist-tun', 'redirect-gateway',
                            'dhcp-option', 'route', 'server',
                            # JSON anahtar kelimeleri
                            '"server"', '"port"', '"password"',
                            '"method"', '"protocol"', '"remarks"',
                            # Shadowsocks/V2Ray
                            'ss://', 'ssr://', 'vmess://', 'trojan://',
                            # Sertifika
                            'BEGIN CERTIFICATE', 'END CERTIFICATE',
                            'BEGIN PRIVATE KEY', 'END PRIVATE KEY'
                        ]
                        
                        text_lower = text.lower()
                        for keyword in vpn_keywords:
                            if keyword.lower() in text_lower:
                                vpn_score += 1
                        
                        total_score = ascii_ratio * 100 + vpn_score * 20
                        
                        best_results.append({
                            'key_str': key_str,
                            'key_type': i,
                            'key_hex': key.hex()[:32],
                            'ascii_ratio': ascii_ratio,
                            'vpn_score': vpn_score,
                            'total_score': total_score,
                            'text': text,
                            'result': result
                        })
                        
                        print(f"  Tip {i}: ASCII {ascii_ratio:.1%}, VPN Score {vpn_score}, Total {total_score:.1f}")
                        
                    except:
                        pass
                        
            except Exception as e:
                continue
    
    # En iyi sonuçları sırala
    best_results.sort(key=lambda x: x['total_score'], reverse=True)
    
    print(f"\n{'='*50}")
    print(f"TOPLAM {len(best_results)} UMUT VERİCİ SONUÇ BULUNDU!")
    print("="*50)
    
    # En iyi 5 sonucu göster
    for i, result in enumerate(best_results[:5]):
        print(f"\n{i+1}. SONUÇ:")
        print(f"   Anahtar: {result['key_str']}")
        print(f"   Hash Tipi: {result['key_type']}")
        print(f"   Key Hex: {result['key_hex']}...")
        print(f"   ASCII: {result['ascii_ratio']:.1%}")
        print(f"   VPN Score: {result['vpn_score']}")
        print(f"   Total Score: {result['total_score']:.1f}")
        print(f"   Metin Önizleme: {result['text'][:150]}...")
        
        # En iyi sonucu dosyaya kaydet
        if i == 0:
            with open("happ_decrypted_final.txt", "w", encoding="utf-8") as f:
                f.write(f"=== HAPP VPN ŞİFRE ÇÖZME SONUCU ===\n")
                f.write(f"Anahtar: {result['key_str']}\n")
                f.write(f"Hash Tipi: {result['key_type']}\n")
                f.write(f"Key Hex: {result['key_hex']}\n")
                f.write(f"ASCII Oranı: {result['ascii_ratio']:.2%}\n")
                f.write(f"VPN Score: {result['vpn_score']}\n")
                f.write(f"Total Score: {result['total_score']:.2f}\n")
                f.write("="*50 + "\n\n")
                f.write(result['text'])
            
            print(f"\n   *** EN İYİ SONUÇ KAYDEDİLDİ: happ_decrypted_final.txt ***")
            
            # VPN config olarak da kaydet
            if result['vpn_score'] > 2:
                with open("happ_vpn_config.ovpn", "w", encoding="utf-8") as f:
                    f.write(result['text'])
                print(f"   *** VPN CONFIG KAYDEDİLDİ: happ_vpn_config.ovpn ***")
            
            # JSON olarak kaydetmeyi dene
            if result['text'].strip().startswith('{') or result['text'].strip().startswith('['):
                try:
                    import json
                    json_data = json.loads(result['text'])
                    with open("happ_vpn_config.json", "w", encoding="utf-8") as f:
                        json.dump(json_data, f, indent=2, ensure_ascii=False)
                    print(f"   *** JSON CONFIG KAYDEDİLDİ: happ_vpn_config.json ***")
                except:
                    pass

def try_aes_decryption():
    """AES şifreleme denemesi"""
    
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("\n" + "="*50)
    print("AES ŞİFRELEME DENEMESİ")
    print("="*50)
    
    # AES anahtarları
    aes_keys = [
        "happvpn",
        "crypto.happ.su", 
        "happ.su",
        "crypt2",
        "happcrypt2"
    ]
    
    for key_str in aes_keys:
        print(f"\nAES Anahtar: {key_str}")
        
        # 16, 24, 32 byte anahtarlar için
        for key_size in [16, 24, 32]:
            try:
                # Anahtarı uygun boyuta getir
                key_hash = hashlib.sha256(key_str.encode()).digest()[:key_size]
                
                # Farklı AES modları dene
                modes = ['ECB', 'CBC']
                
                for mode_name in modes:
                    try:
                        if mode_name == 'ECB':
                            cipher = AES.new(key_hash, AES.MODE_ECB)
                            decrypted = cipher.decrypt(encrypted_data)
                        elif mode_name == 'CBC':
                            # IV'yi verinin başından al
                            iv = encrypted_data[:16]
                            ciphertext = encrypted_data[16:]
                            cipher = AES.new(key_hash, AES.MODE_CBC, iv)
                            decrypted = cipher.decrypt(ciphertext)
                        
                        # Padding'i kaldır
                        try:
                            decrypted = unpad(decrypted, AES.block_size)
                        except:
                            pass  # Padding yoksa devam et
                        
                        # ASCII kontrolü
                        ascii_count = sum(1 for b in decrypted if 32 <= b <= 126)
                        ascii_ratio = ascii_count / len(decrypted)
                        
                        if ascii_ratio > 0.5:
                            try:
                                text = decrypted.decode('utf-8')
                                print(f"  AES-{key_size*8} {mode_name} BAŞARILI!")
                                print(f"  ASCII: {ascii_ratio:.1%}")
                                print(f"  Metin: {text[:100]}...")
                                
                                with open(f"aes_result_{key_str}_{key_size}_{mode_name}.txt", "w", encoding="utf-8") as f:
                                    f.write(text)
                                print(f"  Dosyaya kaydedildi: aes_result_{key_str}_{key_size}_{mode_name}.txt")
                                
                            except:
                                pass
                                
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue

def main():
    print("🔓 HAPP VPN ŞİFRE ÇÖZME - TERSINE MÜHENDİSLİK")
    print("=" * 60)
    
    # Happ'e özel şifreleme denemeleri
    try_happ_specific_decryption()
    
    # AES denemeleri
    try_aes_decryption()
    
    print("\n" + "=" * 60)
    print("🎯 TERSINE MÜHENDİSLİK TAMAMLANDI!")
    print("En iyi sonuçlar dosyalara kaydedildi.")
    print("Eğer VPN config bulunduysa .ovpn veya .json dosyası oluşturuldu.")

if __name__ == "__main__":
    main()
