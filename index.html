
&lt;!DOCTYPE html&gt;
&lt;html lang="tr"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Baglan News - Türkmenistanyň №1 Interaktiw Habar Saýty&lt;/title&gt;
    &lt;style&gt;
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #000000;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Parallax Background */
        .parallax-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #00BFFF, #32CD32, #FFD700, #FF6347, #00BFFF);
            background-size: 500% 500%;
            animation: gradientMove 20s ease infinite;
            z-index: -2;
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            25% { background-position: 100% 50%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
        }

        /* Particles Animation */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            25% { transform: translateY(-30px) rotate(90deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
            75% { transform: translateY(-40px) rotate(270deg); opacity: 0.8; }
        }

        /* Hologram Effect */
        .hologram {
            position: relative;
            background: linear-gradient(45deg, transparent, rgba(0,191,255,0.1), transparent);
            animation: hologramShine 3s infinite;
        }

        @keyframes hologramShine {
            0%, 100% { box-shadow: 0 0 20px rgba(0,191,255,0.3); }
            50% { box-shadow: 0 0 40px rgba(50,205,50,0.5), inset 0 0 20px rgba(255,255,255,0.1); }
        }

        .header {
            background: linear-gradient(135deg, #00BFFF, #32CD32, #FFD700, #00BFFF);
            background-size: 300% 300%;
            animation: gradientShift 6s ease infinite;
            color: white;
            padding: 2rem 0;
            box-shadow: 0 8px 32px rgba(0,191,255,0.4);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 4s infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            33% { background-position: 100% 50%; }
            66% { background-position: 50% 100%; }
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: logoGlow 4s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { text-shadow: 3px 3px 6px rgba(0,0,0,0.4); }
            50% { text-shadow: 3px 3px 20px rgba(255,255,255,0.8), 0 0 30px rgba(0,191,255,0.6); }
        }

        .logo::before {
            content: "🇹🇲";
            font-size: 4rem;
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(5deg); }
            50% { transform: scale(1.2) rotate(-5deg); }
            75% { transform: scale(1.1) rotate(3deg); }
        }

        /* Enhanced Navigation */
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .nav-menu li {
            cursor: pointer;
            padding: 0.8rem 1.5rem;
            border-radius: 30px;
            transition: all 0.4s ease;
            background: rgba(255,255,255,0.15);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .nav-menu li::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,215,0,0.3));
            transition: left 0.4s ease;
        }

        .nav-menu li:hover::before {
            left: 0;
        }

        .nav-menu li:hover {
            transform: translateY(-3px) scale(1.08);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* Game Section */
        .games-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .game-card {
            background: linear-gradient(135deg, #00BFFF, #32CD32);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.4s ease;
        }

        .game-card:hover::before {
            transform: translateX(100%);
        }

        .game-card:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,191,255,0.4);
        }

        .game-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: gameIconBounce 2s ease-in-out infinite;
        }

        @keyframes gameIconBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        /* Creative Section */
        .creative-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .creative-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .creative-card {
            background: linear-gradient(135deg, #FF6347, #FFD700);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .creative-card:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255,99,71,0.4);
        }

        /* AI Chat Bot */
        .chatbot-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 70px;
            height: 70px;
            background: linear-gradient(45deg, #32CD32, #00BFFF, #FFD700);
            background-size: 200% 200%;
            animation: chatbotGlow 3s ease infinite;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        @keyframes chatbotGlow {
            0%, 100% { 
                background-position: 0% 50%; 
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            }
            50% { 
                background-position: 100% 50%; 
                box-shadow: 0 8px 30px rgba(0,191,255,0.5);
            }
        }

        .chatbot-widget:hover {
            transform: scale(1.15);
            box-shadow: 0 10px 35px rgba(0,191,255,0.6);
        }

        /* Music Player */
        .music-player {
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin: 1rem 0;
            backdrop-filter: blur(10px);
        }

        .music-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .music-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #32CD32, #00BFFF);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .music-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,191,255,0.4);
        }

        /* Recipe Section */
        .recipe-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .recipe-card {
            background: linear-gradient(135deg, #FF6347, #FFD700);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.4s ease;
        }

        .recipe-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255,99,71,0.4);
        }

        /* Virtual Tour */
        .virtual-tour {
            background: linear-gradient(135deg, #00BFFF, #32CD32);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .virtual-tour::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,&lt;svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"&gt;&lt;circle cx="20" cy="20" r="2" fill="white" opacity="0.3"/&gt;&lt;circle cx="80" cy="30" r="1.5" fill="white" opacity="0.4"/&gt;&lt;circle cx="60" cy="70" r="1" fill="white" opacity="0.5"/&gt;&lt;circle cx="30" cy="80" r="2.5" fill="white" opacity="0.3"/&gt;&lt;circle cx="50" cy="40" r="1.8" fill="white" opacity="0.4"/&gt;&lt;/svg&gt;');
            animation: tourStars 15s linear infinite;
        }

        @keyframes tourStars {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .virtual-tour:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0,191,255,0.5);
        }

        /* Enhanced Statistics */
        .stats-panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            border-radius: 15px;
            background: linear-gradient(135deg, #00BFFF, #32CD32, #FFD700);
            background-size: 200% 200%;
            animation: statGradient 4s ease infinite;
            color: white;
            transform: perspective(1000px) rotateX(0deg);
            transition: transform 0.4s ease;
        }

        @keyframes statGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .stat-item:hover {
            transform: perspective(1000px) rotateX(15deg) scale(1.08);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
            animation: countUp 3s ease-out;
        }

        @keyframes countUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* News Grid Enhanced */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin: 2rem auto;
            max-width: 1400px;
            padding: 0 20px;
        }

        .news-grid {
            display: grid;
            gap: 2rem;
        }

        .featured-article {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            transition: all 0.4s ease;
            position: relative;
        }

        .featured-article::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0,191,255,0.1), rgba(50,205,50,0.1));
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .featured-article:hover::before {
            opacity: 1;
        }

        .featured-article:hover {
            transform: translateY(-12px) scale(1.03);
        }

        .article-image {
            width: 100%;
            height: 350px;
            background: linear-gradient(45deg, #00BFFF, #32CD32, #FFD700);
            background-size: 200% 200%;
            animation: articleImageGlow 6s ease infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        @keyframes articleImageGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .article-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: imageShine 4s infinite;
        }

        @keyframes imageShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Enhanced Sidebar */
        .sidebar {
            display: grid;
            gap: 2rem;
        }

        .widget {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(15px);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #00BFFF, #32CD32, #FFD700);
            animation: widgetLine 3s ease infinite;
        }

        @keyframes widgetLine {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        /* Game Modal */
        .game-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(10px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .game-content {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 1.2rem;
        }

        /* Quiz Game Styles */
        .quiz-question {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .quiz-option {
            padding: 0.8rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: #e3f2fd;
            border-color: #00BFFF;
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #32CD32;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        /* Memory Game */
        .memory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            max-width: 400px;
            margin: 0 auto;
        }

        .memory-card {
            aspect-ratio: 1;
            background: linear-gradient(45deg, #00BFFF, #32CD32);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .memory-card:hover {
            transform: scale(1.05);
        }

        .memory-card.flipped {
            background: white;
            color: #333;
            border: 2px solid #00BFFF;
        }

        /* Word Search */
        .word-search-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 2px;
            max-width: 400px;
            margin: 1rem auto;
        }

        .word-cell {
            aspect-ratio: 1;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .word-cell:hover {
            background: #e3f2fd;
        }

        .word-cell.selected {
            background: #00BFFF;
            color: white;
        }

        .word-cell.found {
            background: #32CD32;
            color: white;
        }

        /* Crossword */
        .crossword-grid {
            display: grid;
            grid-template-columns: repeat(15, 1fr);
            gap: 1px;
            max-width: 500px;
            margin: 1rem auto;
        }

        .crossword-cell {
            aspect-ratio: 1;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
        }

        .crossword-cell.black {
            background: #333;
        }

        .crossword-cell.white {
            background: white;
        }

        .crossword-cell input {
            width: 100%;
            height: 100%;
            border: none;
            text-align: center;
            font-weight: bold;
            text-transform: uppercase;
        }

        /* Enhanced Footer */
        .footer {
            background: linear-gradient(135deg, #000000, #333333, #000000);
            background-size: 200% 200%;
            animation: footerGradient 8s ease infinite;
            color: white;
            text-align: center;
            padding: 4rem 0;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        @keyframes footerGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #00BFFF, #32CD32, #FFD700, #FF6347, #00BFFF);
            background-size: 200% 200%;
            animation: footerLine 4s ease-in-out infinite;
        }

        @keyframes footerLine {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                justify-content: center;
                gap: 0.5rem;
            }
            
            .nav-menu li {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            .games-grid,
            .creative-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-panel {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Dark Mode Enhanced */
        .dark-mode {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
        }

        .dark-mode .widget,
        .dark-mode .featured-article,
        .dark-mode .games-section,
        .dark-mode .creative-section,
        .dark-mode .recipe-section {
            background: rgba(40,40,40,0.95) !important;
            color: #ffffff !important;
        }

        /* Voice Control Indicator */
        .voice-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            display: none;
            z-index: 3000;
        }

        .voice-wave {
            width: 100px;
            height: 100px;
            border: 3px solid #00BFFF;
            border-radius: 50%;
            margin: 1rem auto;
            animation: voicePulse 1s ease-in-out infinite;
        }

        @keyframes voicePulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        /* AI Writing Assistant */
        .ai-writer {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin: 2rem 0;
        }

        .ai-input {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            margin: 1rem 0;
            font-size: 1rem;
        }

        .ai-output {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            min-height: 100px;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;!-- Parallax Background --&gt;
    &lt;div class="parallax-bg"&gt;&lt;/div&gt;
    
    &lt;!-- Particles --&gt;
    &lt;div class="particles" id="particles"&gt;&lt;/div&gt;

    &lt;!-- AI Chatbot Widget --&gt;
    &lt;div class="chatbot-widget" onclick="openAIChatbot()"&gt;🤖&lt;/div&gt;

    &lt;!-- Voice Control Indicator --&gt;
    &lt;div class="voice-indicator" id="voiceIndicator"&gt;
        &lt;div class="voice-wave"&gt;&lt;/div&gt;
        &lt;div&gt;Ses bilen dolandyryş aktiwleşdi&lt;/div&gt;
        &lt;div style="font-size: 0.9rem; opacity: 0.8;"&gt;Näme etmeli diýiň...&lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Game Modal --&gt;
    &lt;div class="game-modal" id="gameModal"&gt;
        &lt;div class="game-content"&gt;
            &lt;button class="close-btn" onclick="closeGame()"&gt;&amp;times;&lt;/button&gt;
            &lt;div id="gameArea"&gt;&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;header class="header"&gt;
        &lt;div class="container"&gt;
            &lt;div class="header-content"&gt;
                &lt;div class="logo hologram"&gt;
                    &lt;div&gt;
                        &lt;span style="font-size: 3.2rem; font-weight: 900; text-shadow: 4px 4px 8px rgba(0,0,0,0.5);"&gt;
                            Baglan News
                        &lt;/span&gt;
                        &lt;div style="font-size: 1rem; margin-top: -0.5rem; opacity: 0.9; font-weight: 300;"&gt;
                            Türkmenistanyň №1 Interaktiw AI Habar Saýty
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;nav&gt;
                    &lt;ul class="nav-menu"&gt;
                        &lt;li onclick="showSection('baş sahypa')"&gt;🏠 Baş Sahypa&lt;/li&gt;
                        &lt;li onclick="showSection('oýunlar')"&gt;🎮 Oýunlar&lt;/li&gt;
                        &lt;li onclick="showSection('kreatiw')"&gt;🎨 Kreatiw&lt;/li&gt;
                        &lt;li onclick="showSection('medeniýet')"&gt;🎭 Medeniýet&lt;/li&gt;
                        &lt;li onclick="showSection('ai-hyzmat')"&gt;🤖 AI Hyzmat&lt;/li&gt;
                        &lt;li onclick="showSection('virtual-tur')"&gt;🌍 Virtual Tur&lt;/li&gt;
                        &lt;li onclick="toggleVoiceControl()"&gt;🎤 Ses Dolandyryş&lt;/li&gt;
                        &lt;li onclick="toggleSearch()" style="background: rgba(255,255,255,0.2);"&gt;🔍 Gözleg&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/nav&gt;
                
                &lt;!-- Enhanced Search --&gt;
                &lt;div id="searchBox" style="display: none; margin-top: 1rem; width: 100%;"&gt;
                    &lt;div style="display: flex; gap: 0.5rem; max-width: 500px; margin: 0 auto;"&gt;
                        &lt;input type="text" id="searchInput" placeholder="AI bilen gözleg..." 
                               style="flex: 1; padding: 0.8rem 1.5rem; border: none; border-radius: 30px; outline: none; backdrop-filter: blur(10px); background: rgba(255,255,255,0.9);"&gt;
                        &lt;button onclick="searchNews()" style="padding: 0.8rem 2rem; background: linear-gradient(45deg, #32CD32, #00BFFF); color: white; border: none; border-radius: 30px; cursor: pointer;"&gt;🔍 AI Gözleg&lt;/button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/header&gt;

    &lt;!-- Enhanced Statistics Panel --&gt;
    &lt;div class="container"&gt;
        &lt;div class="stats-panel"&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="visitors"&gt;25,847&lt;/span&gt;
                &lt;div&gt;Gündelik Okyjy&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="articles"&gt;3,456&lt;/span&gt;
                &lt;div&gt;Jemi Habar&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="games"&gt;12&lt;/span&gt;
                &lt;div&gt;Interaktiw Oýun&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="aiChats"&gt;1,789&lt;/span&gt;
                &lt;div&gt;AI Söhbetdeşlik&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="recipes"&gt;156&lt;/span&gt;
                &lt;div&gt;Türkmen Nahar&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="stat-item"&gt;
                &lt;span class="stat-number" id="tours"&gt;8&lt;/span&gt;
                &lt;div&gt;Virtual Tur&lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Games Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="games-section"&gt;
            &lt;h2 style="text-align: center; margin-bottom: 2rem; font-size: 2.5rem; background: linear-gradient(45deg, #00BFFF, #32CD32); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"&gt;🎮 Türkmen Oýunlar&lt;/h2&gt;
            &lt;div class="games-grid"&gt;
                &lt;div class="game-card" onclick="startGame('quiz')"&gt;
                    &lt;div class="game-icon"&gt;🧠&lt;/div&gt;
                    &lt;h3&gt;Türkmen Bilim Oýny&lt;/h3&gt;
                    &lt;p&gt;Türkmenistan barada bilim synagy&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="game-card" onclick="startGame('puzzle')"&gt;
                    &lt;div class="game-icon"&gt;🧩&lt;/div&gt;
                    &lt;h3&gt;Habar Puzzle&lt;/h3&gt;
                    &lt;p&gt;Habarlary düzmek oýny&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="game-card" onclick="startGame('wordsearch')"&gt;
                    &lt;div class="game-icon"&gt;🔤&lt;/div&gt;
                    &lt;h3&gt;Söz Tapyş&lt;/h3&gt;
                    &lt;p&gt;Türkmen sözlerini tapmak&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="game-card" onclick="startGame('memory')"&gt;
                    &lt;div class="game-icon"&gt;🎯&lt;/div&gt;
                    &lt;h3&gt;Memory Oýny&lt;/h3&gt;
                    &lt;p&gt;Türkmen şäherleri ýatlamak&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="game-card" onclick="startGame('crossword')"&gt;
                    &lt;div class="game-icon"&gt;📝&lt;/div&gt;
                    &lt;h3&gt;Türkmen Krossword&lt;/h3&gt;
                    &lt;p&gt;Türkmen dilinde krossword&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="game-card" onclick="startGame('trivia')"&gt;
                    &lt;div class="game-icon"&gt;🏆&lt;/div&gt;
                    &lt;h3&gt;Türkmen Trivia&lt;/h3&gt;
                    &lt;p&gt;Medeniýet we taryh soraglary&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Creative Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="creative-section"&gt;
            &lt;h2 style="text-align: center; margin-bottom: 2rem; font-size: 2.5rem; background: linear-gradient(45deg, #FF6347, #FFD700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"&gt;🎨 Kreatiw Bölüm&lt;/h2&gt;
            &lt;div class="creative-grid"&gt;
                &lt;div class="creative-card" onclick="openNewsWriter()"&gt;
                    &lt;h3&gt;📝 Öz Habar Ýaz&lt;/h3&gt;
                    &lt;p&gt;AI kömegi bilen öz habaryňyzy ýazyň&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="creative-card" onclick="openPhotoGallery()"&gt;
                    &lt;h3&gt;📸 Surat Galereýa&lt;/h3&gt;
                    &lt;p&gt;Türkmenistanyň owadan suratlary&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="creative-card" onclick="openVideoGallery()"&gt;
                    &lt;h3&gt;🎬 Wideo Galereýa&lt;/h3&gt;
                    &lt;p&gt;Türkmen medeniýeti wideolary&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="creative-card" onclick="openMusicLibrary()"&gt;
                    &lt;h3&gt;🎵 Ses Kitaphanasy&lt;/h3&gt;
                    &lt;p&gt;Türkmen aýdym-sazlary&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="creative-card" onclick="startVirtualTour()"&gt;
                    &lt;h3&gt;🌍 Virtual Tur&lt;/h3&gt;
                    &lt;p&gt;Türkmenistanyň gözel ýerlerini gezmek&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="creative-card" onclick="openImageEditor()"&gt;
                    &lt;h3&gt;🖼️ Surat Redaktory&lt;/h3&gt;
                    &lt;p&gt;AI bilen surat redaktirlemek&lt;/p&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- AI Writer Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="ai-writer"&gt;
            &lt;h2 style="text-align: center; margin-bottom: 1rem;"&gt;🤖 AI Habar Ýazyjysy&lt;/h2&gt;
            &lt;p style="text-align: center; margin-bottom: 2rem;"&gt;Artificial Intelligence bilen öz habaryňyzy ýazyň&lt;/p&gt;
            &lt;input type="text" class="ai-input" id="aiPrompt" placeholder="Habar temasy ýazyň... (mysal: Türkmenistanyň tehnologik ösüşi)"&gt;
            &lt;button onclick="generateAINews()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #32CD32, #00BFFF); color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 1.1rem;"&gt;✨ AI bilen Habar Döret&lt;/button&gt;
            &lt;div class="ai-output" id="aiOutput"&gt;AI tarapyndan döredilen habar bu ýerde görkeziler...&lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Music Player Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="music-player"&gt;
            &lt;h3&gt;🎵 Türkmen Aýdym-Sazlary&lt;/h3&gt;
            &lt;div style="margin: 1rem 0;"&gt;
                &lt;div id="currentSong"&gt;Häzirki aýdym: Türkmen Owazy&lt;/div&gt;
                &lt;div style="font-size: 0.9rem; opacity: 0.8;"&gt;Aýdymçy: Türkmen Sazandasy&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="music-controls"&gt;
                &lt;button class="music-btn" onclick="previousSong()"&gt;⏮️&lt;/button&gt;
                &lt;button class="music-btn" onclick="toggleMusic()" id="playBtn"&gt;▶️&lt;/button&gt;
                &lt;button class="music-btn" onclick="nextSong()"&gt;⏭️&lt;/button&gt;
                &lt;div style="flex: 1; margin: 0 1rem;"&gt;
                    &lt;div style="background: rgba(255,255,255,0.3); height: 4px; border-radius: 2px; overflow: hidden;"&gt;
                        &lt;div id="musicProgress" style="height: 100%; background: linear-gradient(90deg, #32CD32, #00BFFF); width: 0%; transition: width 0.3s ease;"&gt;&lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;button class="music-btn" onclick="toggleVolume()"&gt;🔊&lt;/button&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Recipe Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="recipe-section"&gt;
            &lt;h2 style="text-align: center; margin-bottom: 2rem; font-size: 2.5rem; background: linear-gradient(45deg, #FF6347, #FFD700); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"&gt;🍽️ Türkmen Nahar Reseptleri&lt;/h2&gt;
            &lt;div class="recipe-card" onclick="showRecipe('plov')"&gt;
                &lt;h3&gt;🍚 Türkmen Palowy&lt;/h3&gt;
                &lt;p&gt;Däbi türkmen palowy resepti. Öý şertlerinde nädip bişirmeli...&lt;/p&gt;
            &lt;/div&gt;
            &lt;div class="recipe-card" onclick="showRecipe('manty')"&gt;
                &lt;h3&gt;🥟 Türkmen Manty&lt;/h3&gt;
                &lt;p&gt;Adaty türkmen manty resepti. Hamyr we iç taýýarlamak...&lt;/p&gt;
            &lt;/div&gt;
            &lt;div class="recipe-card" onclick="showRecipe('shorpa')"&gt;
                &lt;h3&gt;🍲 Türkmen Şorpasy&lt;/h3&gt;
                &lt;p&gt;Tagamly türkmen şorpasy. Eti we gök önümleri bilen...&lt;/p&gt;
            &lt;/div&gt;
            &lt;div class="recipe-card" onclick="showRecipe('gutap')"&gt;
                &lt;h3&gt;🥙 Türkmen Gutaby&lt;/h3&gt;
                &lt;p&gt;Owadan türkmen gutaby. Gök önümler we et bilen...&lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Virtual Tour Section --&gt;
    &lt;div class="container"&gt;
        &lt;div class="virtual-tour" onclick="startVirtualTour()"&gt;
            &lt;h2 style="font-size: 3rem; margin-bottom: 1rem;"&gt;🌍 Türkmenistan Virtual Tur&lt;/h2&gt;
            &lt;p style="font-size: 1.2rem; margin-bottom: 2rem;"&gt;Türkmenistanyň gözel ýerlerini 360° görmek&lt;/p&gt;
            &lt;div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;"&gt;
                &lt;div style="text-align: center;"&gt;
                    &lt;div style="font-size: 3rem;"&gt;🏛️&lt;/div&gt;
                    &lt;div&gt;Aşgabat&lt;/div&gt;
                &lt;/div&gt;
                &lt;div style="text-align: center;"&gt;
                    &lt;div style="font-size: 3rem;"&gt;🔥&lt;/div&gt;
                    &lt;div&gt;Garagum Derwezesi&lt;/div&gt;
                &lt;/div&gt;
                &lt;div style="text-align: center;"&gt;
                    &lt;div style="font-size: 3rem;"&gt;🏔️&lt;/div&gt;
                    &lt;div&gt;Köpetdag&lt;/div&gt;
                &lt;/div&gt;
                &lt;div style="text-align: center;"&gt;
                    &lt;div style="font-size: 3rem;"&gt;🌊&lt;/div&gt;
                    &lt;div&gt;Hazar Deňzi&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Main Content --&gt;
    &lt;main class="main-content"&gt;
        &lt;section class="news-grid"&gt;
            &lt;article class="featured-article hologram"&gt;
                &lt;div class="article-image"&gt;🌟&lt;/div&gt;
                &lt;div style="padding: 2rem;"&gt;
                    &lt;span style="background: linear-gradient(45deg, #00BFFF, #32CD32); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; display: inline-block; margin-bottom: 1rem;"&gt;ESASY HABAR&lt;/span&gt;
                    &lt;h2 style="font-size: 2rem; margin-bottom: 1rem; color: #000000; font-weight: bold;"&gt;Türkmenistanda AI Tehnologiýalary Ösdürilýär&lt;/h2&gt;
                    &lt;p style="color: #666; margin-bottom: 1.5rem; line-height: 1.8;"&gt;Türkmenistanyň AI we maşyn öwrenmek pudagyndaky täze ösüşleri, ýurduň sanly geljegini üpjün edýär. Hünärmenler Türkmenistanyň AI tehnologiýalarynda öňdebaryjy ýurt boljakdygyny aýdýarlar.&lt;/p&gt;
                    
                    &lt;!-- Enhanced Audio Player --&gt;
                    &lt;div style="background: rgba(0,0,0,0.9); color: white; padding: 1rem; border-radius: 15px; display: flex; align-items: center; gap: 1rem; margin: 1rem 0;"&gt;
                        &lt;button onclick="toggleAudio('article1')" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(45deg, #32CD32, #00BFFF); border: none; color: white; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 1.2rem;"&gt;▶️&lt;/button&gt;
                        &lt;div style="flex: 1;"&gt;
                            &lt;div style="background: rgba(255,255,255,0.3); height: 6px; border-radius: 3px; overflow: hidden;"&gt;
                                &lt;div id="progress1" style="height: 100%; background: linear-gradient(90deg, #32CD32, #00BFFF); width: 0%; transition: width 0.3s ease;"&gt;&lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;span&gt;🎧 AI Ses bilen Diňläň&lt;/span&gt;
                    &lt;/div&gt;
                    
                    &lt;div style="display: flex; justify-content: space-between; align-items: center; color: #888; font-size: 0.9rem;"&gt;
                        &lt;span&gt;📅 1 sagat öň • 👁️ 2,547 görüldi&lt;/span&gt;
                        &lt;button onclick="readArticle('ai-tech-news')" style="background: linear-gradient(45deg, #32CD32, #00BFFF); color: white; padding: 0.8rem 2rem; border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s ease;"&gt;Dowamyny Oka&lt;/button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/article&gt;

            &lt;!-- Enhanced Video Player --&gt;
            &lt;div style="width: 100%; height: 250px; background: linear-gradient(45deg, #000, #333, #000); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; cursor: pointer; transition: all 0.4s ease; position: relative; overflow: hidden;" onclick="playVideo('ai-news-video')"&gt;
                &lt;div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(45deg, rgba(0,191,255,0.2), rgba(50,205,50,0.2)); opacity: 0; transition: opacity 0.4s ease;"&gt;&lt;/div&gt;
                &lt;div style="text-align: center; z-index: 1;"&gt;
                    &lt;div style="font-size: 4rem; margin-bottom: 0.5rem;"&gt;📺&lt;/div&gt;
                    &lt;div style="font-size: 1.2rem;"&gt;AI Tehnologiýa Wideosyny Görmek&lt;/div&gt;
                    &lt;div style="font-size: 0.9rem; opacity: 0.8; margin-top: 0.5rem;"&gt;4K Ultra HD • 5:30 min&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;!-- Enhanced News List --&gt;
            &lt;div style="display: grid; gap: 1.5rem;"&gt;
                &lt;div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(15px); padding: 1.5rem; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border-left: 5px solid #00BFFF; transition: all 0.4s ease; cursor: pointer;" onclick="readArticle('economy-ai')"&gt;
                    &lt;h3 style="color: #000000; margin-bottom: 0.8rem; font-size: 1.3rem;"&gt;💰 AI Ykdysadyýetde Täze Mümkinçilikler&lt;/h3&gt;
                    &lt;p style="color: #666; font-size: 1rem; line-height: 1.6;"&gt;Artificial Intelligence tehnologiýalary Türkmenistanyň ykdysadyýetini ösdürýär...&lt;/p&gt;
                    &lt;div style="margin-top: 1rem; display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #888;"&gt;
                        &lt;span&gt;🕒 3 sagat öň&lt;/span&gt;
                        &lt;span&gt;👁️ 1,234 görüldi&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                
                &lt;div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(15px); padding: 1.5rem; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border-left: 5px solid #32CD32; transition: all 0.4s ease; cursor: pointer;" onclick="readArticle('sports-ai')"&gt;
                    &lt;h3 style="color: #000000; margin-bottom: 0.8rem; font-size: 1.3rem;"&gt;⚽ AI Sport Analitikasy&lt;/h3&gt;
                    &lt;p style="color: #666; font-size: 1rem; line-height: 1.6;"&gt;Türkmen sportçylarynyň öndürijiligini AI bilen öwrenmek...&lt;/p&gt;
                    &lt;div style="margin-top: 1rem; display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #888;"&gt;
                        &lt;span&gt;🕒 5 sagat öň&lt;/span&gt;
                        &lt;span&gt;👁️ 987 görüldi&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                
                &lt;div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(15px); padding: 1.5rem; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border-left: 5px solid #FFD700; transition: all 0.4s ease; cursor: pointer;" onclick="readArticle('culture-ai')"&gt;
                    &lt;h3 style="color: #000000; margin-bottom: 0.8rem; font-size: 1.3rem;"&gt;🎭 AI Medeni Taslamalar&lt;/h3&gt;
                    &lt;p style="color: #666; font-size: 1rem; line-height: 1.6;"&gt;Türkmen medeniýetini AI bilen goramak we ösdürmek...&lt;/p&gt;
                    &lt;div style="margin-top: 1rem; display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #888;"&gt;
                        &lt;span&gt;🕒 1 gün öň&lt;/span&gt;
                        &lt;span&gt;👁️ 2,156 görüldi&lt;/span&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/section&gt;

        &lt;!-- Enhanced Sidebar --&gt;
        &lt;aside class="sidebar"&gt;
            &lt;div class="widget hologram"&gt;
                &lt;h3 style="color: #000000; font-size: 1.5rem; margin-bottom: 1.5rem; padding-bottom: 0.8rem; border-bottom: 3px solid #00BFFF;"&gt;🔥 AI Maslahat&lt;/h3&gt;
                &lt;div id="aiRecommendations"&gt;
                    &lt;div style="padding: 1rem; background: linear-gradient(45deg, rgba(0,191,255,0.1), rgba(50,205,50,0.1)); border-radius: 10px; margin-bottom: 1rem; cursor: pointer;" onclick="readArticle('ai-rec1')"&gt;
                        &lt;strong&gt;🤖 AI size maslahat berýär:&lt;/strong&gt;
                        &lt;p style="margin-top: 0.5rem; font-size: 0.9rem;"&gt;Türkmenistanyň tehnologik ösüşi barada has köp okamaly&lt;/p&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;div class="widget"&gt;
                &lt;h3 style="color: #000000; font-size: 1.5rem; margin-bottom: 1.5rem;"&gt;🎮 Çalt Oýun&lt;/h3&gt;
                &lt;div style="display: grid; gap: 0.8rem;"&gt;
                    &lt;button onclick="startQuickGame('quiz')" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #00BFFF, #32CD32); color: white; border: none; border-radius: 15px; cursor: pointer; transition: all 0.3s ease;"&gt;🧠 Çalt Bilim Synagy&lt;/button&gt;
                    &lt;button onclick="startQuickGame('memory')" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #FF6347, #FFD700); color: white; border: none; border-radius: 15px; cursor: pointer; transition: all 0.3s ease;"&gt;🎯 Memory Oýny&lt;/button&gt;
                    &lt;button onclick="startQuickGame('word')" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #32CD32, #00BFFF); color: white; border: none; border-radius: 15px; cursor: pointer; transition: all 0.3s ease;"&gt;🔤 Söz Oýny&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;div class="widget"&gt;
                &lt;h3 style="color: #000000; font-size: 1.5rem; margin-bottom: 1.5rem;"&gt;🌤️ AI Howa Çaklamasy&lt;/h3&gt;
                &lt;div style="background: linear-gradient(135deg, #87CEEB, #00BFFF); color: white; border-radius: 15px; padding: 1.5rem; text-align: center;"&gt;
                    &lt;div style="font-size: 3rem; margin-bottom: 0.5rem;"&gt;☀️&lt;/div&gt;
                    &lt;div style="font-size: 2rem; font-weight: bold;"&gt;29°C&lt;/div&gt;
                    &lt;div&gt;Aşgabat - AI çaklamasy&lt;/div&gt;
                    &lt;div style="font-size: 0.9rem; margin-top: 1rem; opacity: 0.9;"&gt;
                        Ertir: 31°C ☀️ | Öňki gün: 27°C 🌤️
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;

            &lt;div class="widget"&gt;
                &lt;h3 style="color: #000000; font-size: 1.5rem; margin-bottom: 1.5rem;"&gt;🚀 AI Funksiýalar&lt;/h3&gt;
                &lt;div style="display: grid; gap: 0.8rem;"&gt;
                    &lt;button onclick="openAITranslator()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 15px; cursor: pointer;"&gt;🌐 AI Terjimeçi&lt;/button&gt;
                    &lt;button onclick="openAISummarizer()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #f093fb, #f5576c); color: white; border: none; border-radius: 15px; cursor: pointer;"&gt;📝 AI Gysgaltmak&lt;/button&gt;
                    &lt;button onclick="openAIAnalyzer()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; border: none; border-radius: 15px; cursor: pointer;"&gt;📊 AI Derňew&lt;/button&gt;
                    &lt;button onclick="openAIPredictor()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #43e97b, #38f9d7); color: white; border: none; border-radius: 15px; cursor: pointer;"&gt;🔮 AI Çaklama&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/aside&gt;
    &lt;/main&gt;

    &lt;!-- Enhanced Footer --&gt;
    &lt;footer class="footer"&gt;
        &lt;div class="container"&gt;
            &lt;div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 3rem; margin-bottom: 3rem;"&gt;
                &lt;div&gt;
                    &lt;h3 style="color: #00BFFF; margin-bottom: 1.5rem; font-size: 1.8rem;"&gt;🤖 Baglan AI News&lt;/h3&gt;
                    &lt;p style="line-height: 1.8;"&gt;Türkmenistanyň №1 AI-powered habar saýty. Artificial Intelligence, oýunlar, kreatiw funksiýalar we interaktiw medeni mazmuny bilen doly habar tejribesi.&lt;/p&gt;
                &lt;/div&gt;
                &lt;div&gt;
                    &lt;h3 style="color: #32CD32; margin-bottom: 1.5rem;"&gt;🎮 Funksiýalar&lt;/h3&gt;
                    &lt;ul style="list-style: none; line-height: 2;"&gt;
                        &lt;li onclick="startGame('quiz')" style="cursor: pointer; transition: all 0.3s ease;"&gt;🧠 AI Bilim Oýunlary&lt;/li&gt;
                        &lt;li onclick="openNewsWriter()" style="cursor: pointer; transition: all 0.3s ease;"&gt;📝 AI Habar Ýazyjysy&lt;/li&gt;
                        &lt;li onclick="startVirtualTour()" style="cursor: pointer; transition: all 0.3s ease;"&gt;🌍 Virtual Türkmenistan&lt;/li&gt;
                        &lt;li onclick="openMusicLibrary()" style="cursor: pointer; transition: all 0.3s ease;"&gt;🎵 Türkmen Aýdymlary&lt;/li&gt;
                        &lt;li onclick="showRecipe('plov')" style="cursor: pointer; transition: all 0.3s ease;"&gt;🍽️ Milli Naharlar&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
                &lt;div&gt;
                    &lt;h3 style="color: #FFD700; margin-bottom: 1.5rem;"&gt;🚀 AI Hyzmatlar&lt;/h3&gt;
                    &lt;ul style="list-style: none; line-height: 2;"&gt;
                        &lt;li onclick="openAITranslator()" style="cursor: pointer; transition: all 0.3s ease;"&gt;🌐 AI Terjimeçi&lt;/li&gt;
                        &lt;li onclick="openAIChatbot()" style="cursor: pointer; transition: all 0.3s ease;"&gt;💬 AI Söhbetdeşlik&lt;/li&gt;
                        &lt;li onclick="toggleVoiceControl()" style="cursor: pointer; transition: all 0.3s ease;"&gt;🎤 Ses Dolandyryş&lt;/li&gt;
                        &lt;li onclick="openAIAnalyzer()" style="cursor: pointer; transition: all 0.3s ease;"&gt;📊 AI Derňew&lt;/li&gt;
                        &lt;li onclick="openImageEditor()" style="cursor: pointer; transition: all 0.3s ease;"&gt;🖼️ AI Surat Redaktor&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div style="border-top: 2px solid #333; padding-top: 2rem; text-align: center;"&gt;
                &lt;p style="font-size: 1.1rem;"&gt;&amp;copy; 2024 Baglan AI News. Ähli hukuklar goragly. Türkmenistanyň №1 AI-Powered Interaktiw Habar Saýty&lt;/p&gt;
                &lt;p style="margin-top: 1rem; opacity: 0.8;"&gt;🤖 Artificial Intelligence • 🎮 Interaktiw Oýunlar • 🎨 Kreatiw Funksiýalar • 🌍 Virtual Tur&lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/footer&gt;

    &lt;script&gt;
        // Enhanced Particles Animation
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i &lt; 80; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 6 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                
                // Different particle colors
                const colors = ['rgba(0,191,255,0.8)', 'rgba(50,205,50,0.8)', 'rgba(255,215,0,0.8)', 'rgba(255,99,71,0.8)'];
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                
                particlesContainer.appendChild(particle);
            }
        }

        // Enhanced Statistics Animation
        function animateStats() {
            const stats = [
                { id: 'visitors', target: 25847 },
                { id: 'articles', target: 3456 },
                { id: 'games', target: 12 },
                { id: 'aiChats', target: 1789 },
                { id: 'recipes', target: 156 },
                { id: 'tours', target: 8 }
            ];

            stats.forEach(stat =&gt; {
                const element = document.getElementById(stat.id);
                let current = 0;
                const increment = stat.target / 150;
                const timer = setInterval(() =&gt; {
                    current += increment;
                    if (current &gt;= stat.target) {
                        current = stat.target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current).toLocaleString();
                }, 15);
            });
        }

        // Game Functions
        let currentGame = null;
        let gameScore = 0;

        function startGame(gameType) {
            currentGame = gameType;
            const modal = document.getElementById('gameModal');
            const gameArea = document.getElementById('gameArea');
            
            modal.style.display = 'flex';
            
            switch(gameType) {
                case 'quiz':
                    startQuizGame();
                    break;
                case 'memory':
                    startMemoryGame();
                    break;
                case 'wordsearch':
                    startWordSearchGame();
                    break;
                case 'puzzle':
                    startPuzzleGame();
                    break;
                case 'crossword':
                    startCrosswordGame();
                    break;
                case 'trivia':
                    startTriviaGame();
                    break;
            }
        }

        function closeGame() {
            document.getElementById('gameModal').style.display = 'none';
            currentGame = null;
            gameScore = 0;
        }

        // Quiz Game
        function startQuizGame() {
            const questions = [
                {
                    question: "Türkmenistanyň paýtagty haýsy şäher?",
                    options: ["Aşgabat", "Türkmenabat", "Mary", "Daşoguz"],
                    correct: 0
                },
                {
                    question: "Türkmenistanyň milli baýramy haçan bellenilýär?",
                    options: ["27 oktýabr", "12 dekabr", "1 ýanwar", "9 maý"],
                    correct: 0
                },
                {
                    question: "Türkmenistanyň iň uly derýasy haýsy?",
                    options: ["Amyderýa", "Murgap", "Tejen", "Atrek"],
                    correct: 0
                },
                {
                    question: "Garagum çölüniň meýdany näçe km²?",
                    options: ["350,000", "450,000", "550,000", "250,000"],
                    correct: 0
                }
            ];

            let currentQuestion = 0;
            gameScore = 0;

            function showQuestion() {
                const gameArea = document.getElementById('gameArea');
                const q = questions[currentQuestion];
                
                gameArea.innerHTML = `
                    &lt;h2&gt;🧠 Türkmen Bilim Oýny&lt;/h2&gt;
                    &lt;div style="margin: 2rem 0;"&gt;
                        &lt;div style="background: linear-gradient(45deg, #00BFFF, #32CD32); color: white; padding: 1rem; border-radius: 10px; text-align: center; margin-bottom: 1rem;"&gt;
                            Sorag ${currentQuestion + 1}/${questions.length} • Bal: ${gameScore}
                        &lt;/div&gt;
                        &lt;div class="quiz-question"&gt;${q.question}&lt;/div&gt;
                        &lt;div class="quiz-options"&gt;
                            ${q.options.map((option, index) =&gt; 
                                `&lt;div class="quiz-option" onclick="selectAnswer(${index})"&gt;${option}&lt;/div&gt;`
                            ).join('')}
                        &lt;/div&gt;
                    &lt;/div&gt;
                `;
            }

            window.selectAnswer = function(selected) {
                const options = document.querySelectorAll('.quiz-option');
                const correct = questions[currentQuestion].correct;
                
                options[selected].classList.add(selected === correct ? 'correct' : 'wrong');
                options[correct].classList.add('correct');
                
                if (selected === correct) {
                    gameScore += 10;
                }
                
                setTimeout(() =&gt; {
                    currentQuestion++;
                    if (currentQuestion &lt; questions.length) {
                        showQuestion();
                    } else {
                        showQuizResults();
                    }
                }, 1500);
            };

            function showQuizResults() {
                const gameArea = document.getElementById('gameArea');
                const percentage = (gameScore / (questions.length * 10)) * 100;
                let message = "";
                
                if (percentage &gt;= 80) message = "🏆 Ajaýyp! Siz Türkmenistan hünärmeni!";
                else if (percentage &gt;= 60) message = "👍 Gowy! Bilim derejeňiz ýokary!";
                else if (percentage &gt;= 40) message = "📚 Orta netije. Has köp okamaly!";
                else message = "💪 Başlangyjy dereje. Dowam ediň!";
                
                gameArea.innerHTML = `
                    &lt;h2&gt;🎉 Oýun Tamamlandy!&lt;/h2&gt;
                    &lt;div style="text-align: center; margin: 2rem 0;"&gt;
                        &lt;div style="font-size: 4rem; margin-bottom: 1rem;"&gt;🏆&lt;/div&gt;
                        &lt;div style="font-size: 2rem; margin-bottom: 1rem;"&gt;Bal: ${gameScore}/${questions.length * 10}&lt;/div&gt;
                        &lt;div style="font-size: 1.5rem; margin-bottom: 2rem;"&gt;${percentage}%&lt;/div&gt;
                        &lt;div style="background: linear-gradient(45deg, #32CD32, #00BFFF); color: white; padding: 1rem; border-radius: 10px; margin-bottom: 2rem;"&gt;
                            ${message}
                        &lt;/div&gt;
                        &lt;button onclick="startGame('quiz')" style="padding: 1rem 2rem; background: linear-gradient(45deg, #00BFFF, #32CD32); color: white; border: none; border-radius: 10px; cursor: pointer; margin-right: 1rem;"&gt;🔄 Gaýtadan Oýna&lt;/button&gt;
                        &lt;button onclick="closeGame()" style="padding: 1rem 2rem; background: #666; color: white; border: none; border-radius: 10px; cursor: pointer;"&gt;❌ Ýap&lt;/button&gt;
                    &lt;/div&gt;
                `;
            }

            showQuestion();
        }

        // Memory Game
        function startMemoryGame() {
            const cities = ['🏛️', '🏔️', '🌊', '🔥', '🌾', '⛰️', '🏜️', '🌴'];
            const cards = [...cities, ...cities].sort(() =&gt; Math.random() - 0.5);
            let flippedCards = [];
            let matchedPairs = 0;
            let moves = 0;

            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = `
                &lt;h2&gt;🎯 Türkmen Şäherler Memory Oýny&lt;/h2&gt;
                &lt;div style="text-align: center; margin: 1rem 0;"&gt;
                    &lt;span style="background: linear-gradient(45deg, #00BFFF, #32CD32); color: white; padding: 0.5rem 1rem; border-radius: 10px;"&gt;
                        Hereketler: &lt;span id="moves"&gt;0&lt;/span&gt; • Jübütler: &lt;span id="pairs"&gt;0&lt;/span&gt;/8
                    &lt;/span&gt;
                &lt;/div&gt;
                &lt;div class="memory-grid"&gt;
                    ${cards.map((card, index) =&gt; 
                        `&lt;div class="memory-card" data-card="${card}" data-index="${index}" onclick="flipCard(${index})"&gt;❓&lt;/div&gt;`
                    ).join('')}
                &lt;/div&gt;
            `;

            window.flipCard = function(index) {
                const card = document.querySelector(`[data-index="${index}"]`);
                if (card.classList.contains('flipped') || flippedCards.length === 2) return;

                card.classList.add('flipped');
                card.textContent = card.dataset.card;
                flippedCards.push({element: card, value: card.dataset.card, index});

                if (flippedCards.length === 2) {
                    moves++;
                    document.getElementById('moves').textContent = moves;
                    
                    setTimeout(() =&gt; {
                        if (flippedCards[0].value === flippedCards[1].value) {
                            matchedPairs++;
                            document.getElementById('pairs').textContent = matchedPairs;
                            
                            if (matchedPairs === 8) {
                                setTimeout(() =&gt; {
                                    alert(`🎉 Gutlag! ${moves} hereketde tamamladyňyz!\n\n🏆 Netije: ${moves &lt;= 20 ? 'Ajaýyp!' : moves &lt;= 30 ? 'Gowy!' : 'Dowam ediň!'}`);
                                }, 500);
                            }
                        } else {
                            flippedCards.forEach(card =&gt; {
                                card.element.classList.remove('flipped');
                                card.element.textContent = '❓';
                            });
                        }
                        flippedCards = [];
                    }, 1000);
                }
            };
        }

        // Word Search Game
        function startWordSearchGame() {
            const words = ['AŞGABAT', 'TÜRKMEN', 'MARY', 'BALKAN', 'LEBAP'];
            const gridSize = 10;
            const grid = Array(gridSize).fill().map(() =&gt; Array(gridSize).fill(''));
            
            // Place words randomly
            words.forEach(word =&gt; {
                let placed = false;
                while (!placed) {
                    const direction = Math.random() &lt; 0.5 ? 'horizontal' : 'vertical';
                    const row = Math.floor(Math.random() * gridSize);
                    const col = Math.floor(Math.random() * gridSize);
                    
                    if (direction === 'horizontal' &amp;&amp; col + word.length &lt;= gridSize) {
                        let canPlace = true;
                        for (let i = 0; i &lt; word.length; i++) {
                            if (grid[row][col + i] !== '' &amp;&amp; grid[row][col + i] !== word[i]) {
                                canPlace = false;
                                break;
                            }
                        }
                        if (canPlace)