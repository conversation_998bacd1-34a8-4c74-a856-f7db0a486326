#!/usr/bin/env python3
import base64
import hashlib
import re
import json
import binascii

def deep_vpn_extraction():
    """Derin VPN kodu çıkarma"""
    
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("🔍 DERİN VPN KOD ÇIKARMA")
    print("=" * 60)
    
    # En umut verici anahtarlar (önceki analizlerden)
    promising_keys = [
        "happvpn", "SHADOWSOCKS", "TUNNEL", "config", "default",
        "crypto.happ.su", "happ.su", "password", "secret"
    ]
    
    all_found_texts = []
    
    for key_str in promising_keys:
        print(f"\n🔑 Anahtar analizi: {key_str}")
        
        # Farklı hash yöntemleri
        key_variants = [
            key_str.encode('utf-8'),
            hashlib.md5(key_str.encode()).digest(),
            hashlib.sha1(key_str.encode()).digest(),
            hashlib.sha256(key_str.encode()).digest(),
            hashlib.md5(key_str.encode()).digest()[:16],
            hashlib.sha1(key_str.encode()).digest()[:16],
            hashlib.sha256(key_str.encode()).digest()[:16],
            hashlib.sha256(key_str.encode()).digest()[:32],
        ]
        
        for i, key in enumerate(key_variants):
            try:
                # XOR ile çöz
                result = bytes([encrypted_data[j] ^ key[j % len(key)] for j in range(len(encrypted_data))])
                
                # ASCII kontrolü
                ascii_count = sum(1 for b in result if 32 <= b <= 126)
                ascii_ratio = ascii_count / len(result)
                
                if ascii_ratio > 0.3:  # %30'dan fazla ASCII
                    try:
                        text = result.decode('utf-8', errors='ignore')
                        
                        # VPN protokol pattern'leri ara
                        patterns = {
                            'vless': r'vless://[^\s\n\r]+',
                            'vmess': r'vmess://[^\s\n\r]+',
                            'ss': r'ss://[^\s\n\r]+',
                            'ssr': r'ssr://[^\s\n\r]+',
                            'trojan': r'trojan://[^\s\n\r]+',
                            'http': r'https?://[^\s\n\r]+',
                            'socks': r'socks[45]?://[^\s\n\r]+',
                        }
                        
                        found_any = False
                        found_codes = {}
                        
                        for protocol, pattern in patterns.items():
                            matches = re.findall(pattern, text, re.IGNORECASE)
                            if matches:
                                found_codes[protocol] = matches
                                found_any = True
                        
                        # Base64 encoded strings ara (VPN config'lerde yaygın)
                        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
                        base64_matches = re.findall(base64_pattern, text)
                        
                        # JSON pattern ara
                        json_pattern = r'\{[^{}]*"[^"]*"[^{}]*:[^{}]*\}'
                        json_matches = re.findall(json_pattern, text)
                        
                        # Server/IP pattern ara
                        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
                        ip_matches = re.findall(ip_pattern, text)
                        
                        # Domain pattern ara
                        domain_pattern = r'[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*\.[a-zA-Z]{2,}'
                        domain_matches = re.findall(domain_pattern, text)
                        
                        if found_any or base64_matches or json_matches or ip_matches or domain_matches:
                            print(f"  ✅ Tip {i}: ASCII {ascii_ratio:.1%}")
                            
                            if found_codes:
                                for protocol, codes in found_codes.items():
                                    print(f"    🔗 {protocol.upper()}: {len(codes)} kod")
                                    for code in codes[:2]:  # İlk 2'sini göster
                                        print(f"      {code[:80]}...")
                            
                            if base64_matches:
                                print(f"    📦 Base64: {len(base64_matches)} string")
                            
                            if json_matches:
                                print(f"    📋 JSON: {len(json_matches)} parça")
                            
                            if ip_matches:
                                print(f"    🌐 IP: {len(ip_matches)} adres")
                                for ip in ip_matches[:3]:
                                    print(f"      {ip}")
                            
                            if domain_matches:
                                print(f"    🏠 Domain: {len(domain_matches)} adres")
                                for domain in domain_matches[:3]:
                                    print(f"      {domain}")
                            
                            # Metni kaydet
                            all_found_texts.append({
                                'key': key_str,
                                'type': i,
                                'ascii_ratio': ascii_ratio,
                                'text': text,
                                'codes': found_codes,
                                'base64': base64_matches,
                                'json': json_matches,
                                'ips': ip_matches,
                                'domains': domain_matches
                            })
                    
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue
    
    # En iyi sonuçları kaydet
    if all_found_texts:
        print(f"\n{'='*60}")
        print(f"🎯 TOPLAM {len(all_found_texts)} SONUÇ BULUNDU!")
        print("="*60)
        
        # En iyi sonuçları sırala (ASCII oranına göre)
        all_found_texts.sort(key=lambda x: x['ascii_ratio'], reverse=True)
        
        all_vpn_codes = []
        all_ips = []
        all_domains = []
        
        for i, result in enumerate(all_found_texts[:5]):  # En iyi 5 sonuç
            print(f"\n📋 SONUÇ {i+1}:")
            print(f"   🔑 Anahtar: {result['key']}")
            print(f"   📊 ASCII: {result['ascii_ratio']:.1%}")
            
            # VPN kodlarını topla
            if result['codes']:
                for protocol, codes in result['codes'].items():
                    print(f"   🔗 {protocol.upper()}: {len(codes)} kod")
                    all_vpn_codes.extend(codes)
            
            # IP'leri topla
            if result['ips']:
                print(f"   🌐 IP Adresleri: {len(result['ips'])}")
                all_ips.extend(result['ips'])
            
            # Domain'leri topla
            if result['domains']:
                print(f"   🏠 Domain'ler: {len(result['domains'])}")
                all_domains.extend(result['domains'])
            
            # Tam metni dosyaya kaydet
            filename = f"DECODED_TEXT_{i+1}_{result['key']}.txt"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"=== ÇÖZÜLEN METİN {i+1} ===\n")
                f.write(f"Anahtar: {result['key']}\n")
                f.write(f"Tip: {result['type']}\n")
                f.write(f"ASCII Oranı: {result['ascii_ratio']:.2%}\n")
                f.write("="*50 + "\n\n")
                f.write(result['text'])
            
            print(f"   💾 Kaydedildi: {filename}")
        
        # Tüm VPN kodlarını kaydet
        if all_vpn_codes:
            with open("EXTRACTED_VPN_CODES.txt", "w", encoding="utf-8") as f:
                f.write("🔐 ÇIKARILAN VPN KODLARI\n")
                f.write("=" * 40 + "\n\n")
                for i, code in enumerate(set(all_vpn_codes)):  # Tekrarları kaldır
                    f.write(f"{i+1}. {code}\n\n")
            
            print(f"\n🎉 {len(set(all_vpn_codes))} BENZERSIZ VPN KODU BULUNDU!")
            print("💾 Kaydedildi: EXTRACTED_VPN_CODES.txt")
        
        # IP adreslerini kaydet
        if all_ips:
            with open("EXTRACTED_IPS.txt", "w", encoding="utf-8") as f:
                f.write("🌐 ÇIKARILAN IP ADRESLERİ\n")
                f.write("=" * 40 + "\n\n")
                for i, ip in enumerate(set(all_ips)):
                    f.write(f"{i+1}. {ip}\n")
            
            print(f"🌐 {len(set(all_ips))} IP adresi bulundu: EXTRACTED_IPS.txt")
        
        # Domain'leri kaydet
        if all_domains:
            with open("EXTRACTED_DOMAINS.txt", "w", encoding="utf-8") as f:
                f.write("🏠 ÇIKARILAN DOMAIN'LER\n")
                f.write("=" * 40 + "\n\n")
                for i, domain in enumerate(set(all_domains)):
                    f.write(f"{i+1}. {domain}\n")
            
            print(f"🏠 {len(set(all_domains))} domain bulundu: EXTRACTED_DOMAINS.txt")
    
    else:
        print("❌ Maalesef hiçbir VPN kodu bulunamadı.")

if __name__ == "__main__":
    deep_vpn_extraction()
