import os
import logging
import requests
from datetime import datetime, timedelta
from telegram import Update, ChatPermissions
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ChatMemberHandler
)
from dotenv import load_dotenv

# Environment Setup
load_dotenv()

# Logging Configuration
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    filename='bot.log',
    filemode='a'
)
logger = logging.getLogger(__name__)

# Configuration
TOKEN = "7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU"  # .env'den almadan
ADMINS = list(map(int, os.getenv("ADMINS").split(',')))
WEATHER_API_KEY = os.getenv("WEATHER_API_KEY")
MAX_WARNINGS = 3

# Data Structures
BAD_WORDS = ["küfür", "spam", "hack"]
AUTO_REPLIES = {
    "merhaba": "Selam! Kurallar için /rules yazabilirsin.",
    "yardım": "Yardım menüsü için /help yazın."
}
user_data = {
    'warnings': {},
    'muted': {},
    'last_message': {}
}

# Decorator for admin commands
def admin_only(func):
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
        if update.effective_user.id not in ADMINS:
            await update.message.reply_text("❌ Bu komutu kullanma yetkiniz yok!")
            return
        return await func(update, context)
    return wrapper

# Command Handlers
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text(
        "🤖 **Grup Yönetim Botu**\n"
        "Komut listesi için /help yazın\n"
        "Kurallar için /rules yazın"
    )

async def help(update: Update, context: ContextTypes.DEFAULT_TYPE):
    help_text = (
        "📜 **Komut Listesi**\n"
        "/start - Botu başlat\n"
        "/help - Tüm komutlar\n"
        "/rules - Grup kuralları\n"
        "/warn - Kullanıcıyı uyar\n"
        "/mute [süre] - Kullanıcıyı sustur\n"
        "/unmute - Susturmayı kaldır\n"
        "/ban - Kullanıcıyı yasakla\n"
        "/unban - Yasağı kaldır\n"
        "/kick - Kullanıcıyı at\n"
        "/stats - Grup istatistikleri\n"
        "/weather [şehir] - Hava durumu\n"
        "/report - Mesajı raporla"
    )
    await update.message.reply_text(help_text)

async def rules(update: Update, context: ContextTypes.DEFAULT_TYPE):
    rules_text = (
        "📜 **Grup Kuralları**\n"
        "1. Küfür ve hakaret yasak\n"
        "2. Spam yasak\n"
        "3. Reklam paylaşımı yasak\n"
        "4. Yetkililere saygılı olun\n"
        "5. Konu dışı tartışmalar yapmayın"
    )
    await update.message.reply_text(rules_text)

@admin_only
async def warn(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message.reply_to_message:
        await update.message.reply_text("⚠️ Lütfen bir mesajı yanıtlayın!")
        return

    user = update.message.reply_to_message.from_user
    user_id = user.id
    
    user_data['warnings'][user_id] = user_data['warnings'].get(user_id, 0) + 1

    if user_data['warnings'][user_id] >= MAX_WARNINGS:
        await update.message.chat.ban_member(user_id)
        await update.message.reply_text(f"🚫 {user.first_name} {MAX_WARNINGS} uyarı aldığı için yasaklandı!")
        del user_data['warnings'][user_id]
    else:
        await update.message.reply_text(
            f"⚠️ {user.first_name} uyarıldı! ({user_data['warnings'][user_id]}/{MAX_WARNINGS})"
        )

@admin_only
async def mute(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        duration = int(context.args[0]) if context.args else 15
    except (ValueError, IndexError):
        await update.message.reply_text("⚠️ Geçersiz süre! Örnek: /mute 30")
        return

    user = update.message.reply_to_message.from_user
    until_date = datetime.now() + timedelta(minutes=duration)
    
    await context.bot.restrict_chat_member(
        chat_id=update.message.chat.id,
        user_id=user.id,
        permissions=ChatPermissions(
            can_send_messages=False,
            can_send_media_messages=False,
            can_send_other_messages=False,
            can_add_web_page_previews=False
        ),
        until_date=until_date
    )
    
    user_data['muted'][user.id] = until_date
    await update.message.reply_text(f"🔇 {user.first_name} {duration} dakika susturuldu!")

@admin_only
async def unmute(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message.reply_to_message:
        await update.message.reply_text("⚠️ Lütfen bir kullanıcıyı yanıtlayın!")
        return

    user = update.message.reply_to_message.from_user
    await context.bot.restrict_chat_member(
        chat_id=update.message.chat.id,
        user_id=user.id,
        permissions=ChatPermissions(
            can_send_messages=True,
            can_send_media_messages=True,
            can_send_other_messages=True,
            can_add_web_page_previews=True
        )
    )
    
    if user.id in user_data['muted']:
        del user_data['muted'][user.id]
    await update.message.reply_text(f"🔊 {user.first_name} susturması kaldırıldı!")

@admin_only
async def ban(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message.reply_to_message:
        await update.message.reply_text("⚠️ Lütfen bir kullanıcıyı yanıtlayın!")
        return

    user = update.message.reply_to_message.from_user
    await update.message.chat.ban_member(user.id)
    await update.message.reply_text(f"🚫 {user.first_name} yasaklandı!")

@admin_only
async def unban(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not context.args:
        await update.message.reply_text("⚠️ Kullanıcı ID girin!\nÖrnek: /unban 123456789")
        return

    try:
        user_id = int(context.args[0])
        await update.message.chat.unban_member(user_id)
        await update.message.reply_text(f"✅ Kullanıcı (ID: {user_id}) yasağı kaldırıldı!")
    except ValueError:
        await update.message.reply_text("⚠️ Geçersiz kullanıcı ID formatı!")

@admin_only
async def kick(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message.reply_to_message:
        await update.message.reply_text("⚠️ Lütfen bir kullanıcıyı yanıtlayın!")
        return

    user = update.message.reply_to_message.from_user
    await update.message.chat.ban_member(user.id)
    await update.message.chat.unban_member(user.id)
    await update.message.reply_text(f"👢 {user.first_name} gruptan atıldı!")

async def stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    member_count = await update.message.chat.get_member_count()
    await update.message.reply_text(f"👥 Üye Sayısı: {member_count}")

async def weather(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not context.args:
        await update.message.reply_text("⚠️ Lütfen bir şehir adı girin!\nÖrnek: /weather Istanbul")
        return
    
    city = " ".join(context.args)
    url = f"http://api.openweathermap.org/data/2.5/weather?q={city}&appid={WEATHER_API_KEY}&units=metric&lang=tr"
    
    try:
        response = requests.get(url, timeout=10)
        data = response.json()
        
        if data.get("cod") != 200:
            await update.message.reply_text(f"❌ Hata: {data.get('message', 'Bilinmeyen hata')}")
        else:
            temp = data["main"]["temp"]
            desc = data["weather"][0]["description"].capitalize()
            humidity = data["main"]["humidity"]
            await update.message.reply_text(
                f"🌤️ {city} hava durumu:\n"
                f"• Sıcaklık: {temp}°C\n"
                f"• Durum: {desc}\n"
                f"• Nem: %{humidity}"
            )
    except requests.exceptions.Timeout:
        await update.message.reply_text("⌛ Hava durumu servisine bağlanamadı")
    except Exception as e:
        await update.message.reply_text("❌ Teknik bir sorun oluştu")
        logger.error(f"Hava durumu hatası: {str(e)}")

# Message Handlers
async def content_filter(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Küfür filtresi
    if update.message.text and any(word in update.message.text.lower() for word in BAD_WORDS):
        await update.message.delete()
        await update.message.reply_text(f"⚠️ {update.message.from_user.first_name}, uygunsuz içerik!")
        return

    # Flood kontrol
    user_id = update.message.from_user.id
    now = datetime.now()
    
    if user_id in user_data['last_message']:
        last_msg_time = user_data['last_message'][user_id]
        if (now - last_msg_time).total_seconds() < 2:
            await update.message.delete()
            await update.message.reply_text("⏳ Lütfen mesajlarınızı yavaş gönderin!")
            return
    
    user_data['last_message'][user_id] = now

async def auto_replies(update: Update, context: ContextTypes.DEFAULT_TYPE):
    text = update.message.text.lower()
    for keyword, response in AUTO_REPLIES.items():
        if keyword in text:
            await update.message.reply_text(response)
            break

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logger.error(msg="Exception while handling an update:", exc_info=context.error)

if __name__ == "__main__":
    app = Application.builder().token(TOKEN).build()

    # Komutlar
    commands = [
        CommandHandler("start", start),
        CommandHandler("help", help),
        CommandHandler("rules", rules),
        CommandHandler("warn", warn),
        CommandHandler("mute", mute),
        CommandHandler("unmute", unmute),
        CommandHandler("ban", ban),
        CommandHandler("unban", unban),
        CommandHandler("kick", kick),
        CommandHandler("stats", stats),
        CommandHandler("weather", weather)
    ]
    
    for command in commands:
        app.add_handler(command)

    # Mesaj işleyiciler
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, content_filter))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, auto_replies))

    # Hata yönetimi
    app.add_error_handler(error_handler)

    logger.info("Bot başlatılıyor...")
    app.run_polling()