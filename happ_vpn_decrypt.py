#!/usr/bin/env python3
import base64
import hashlib
import json

def try_common_vpn_keys(encrypted_data):
    """VPN uygulamalarında yaygın kullanılan anahtarları dene"""
    
    common_keys = [
        # VPN ile ilgili yaygın anahtarlar
        b"happvpn",
        b"HAPPVPN", 
        b"happ_vpn",
        b"vpnconfig",
        b"openvpn",
        b"wireguard",
        b"v2ray",
        b"shadowsocks",
        b"trojan",
        b"vmess",
        
        # Hash'lenmiş versiyonlar
        hashlib.md5(b"happvpn").digest(),
        hashlib.sha1(b"happvpn").digest()[:16],
        hashlib.sha256(b"happvpn").digest()[:16],
        hashlib.md5(b"happ").digest(),
        hashlib.sha1(b"happ").digest()[:16],
    ]
    
    print("=== VPN ANAHTAR DENEMELERİ ===")
    
    for i, key in enumerate(common_keys):
        print(f"\nAnahtar {i+1}: {key}")
        
        # XOR ile çöz
        result = bytes([encrypted_data[j] ^ key[j % len(key)] for j in range(len(encrypted_data))])
        
        # ASCII kontrolü
        ascii_count = sum(1 for b in result if 32 <= b <= 126)
        ascii_ratio = ascii_count / len(result)
        
        print(f"ASCII oranı: {ascii_ratio:.2%}")
        
        if ascii_ratio > 0.7:
            try:
                text = result.decode('utf-8', errors='ignore')
                print(f"BAŞARILI! Sonuç: {text[:100]}...")
                
                with open(f"vpn_config_{i+1}.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                print(f"VPN config dosyaya kaydedildi: vpn_config_{i+1}.txt")
                
                return text
                
            except Exception as e:
                print(f"UTF-8 çözme hatası: {e}")
    
    return None

def try_vpn_config_patterns(data):
    """VPN config desenlerini ara"""
    
    print("\n=== VPN CONFIG DESEN ARAMA ===")
    
    # VPN config'lerde yaygın olan stringler
    vpn_patterns = [
        b"client",
        b"remote",
        b"port",
        b"proto",
        b"dev",
        b"ca",
        b"cert", 
        b"key",
        b"cipher",
        b"auth",
        b"verb",
        b"mute",
        b"resolv-retry",
        b"nobind",
        b"persist-key",
        b"persist-tun",
        b"redirect-gateway",
        b"dhcp-option",
        b"BEGIN CERTIFICATE",
        b"END CERTIFICATE",
        b"BEGIN PRIVATE KEY",
        b"END PRIVATE KEY"
    ]
    
    # Her byte pozisyonu için XOR anahtarı dene
    best_matches = []
    
    for xor_key in range(256):
        xor_result = bytes([b ^ xor_key for b in data])
        
        matches = 0
        for pattern in vpn_patterns:
            if pattern in xor_result:
                matches += 1
        
        if matches > 0:
            best_matches.append((xor_key, matches, xor_result))
    
    # En iyi eşleşmeleri sırala
    best_matches.sort(key=lambda x: x[1], reverse=True)
    
    print(f"Toplam {len(best_matches)} anahtar VPN deseni buldu")
    
    for i, (key, matches, result) in enumerate(best_matches[:5]):
        print(f"\nAnahtar {key:02x}: {matches} VPN deseni bulundu")
        
        try:
            text = result.decode('utf-8', errors='ignore')
            print(f"Metin: {text[:200]}...")
            
            if matches >= 3:  # 3 veya daha fazla VPN deseni varsa kaydet
                with open(f"vpn_pattern_{key:02x}.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                print(f"Dosyaya kaydedildi: vpn_pattern_{key:02x}.txt")
                
        except Exception as e:
            print(f"UTF-8 çözme hatası: {e}")

def analyze_as_json_or_config(data):
    """JSON veya config formatı olarak analiz et"""
    
    print("\n=== JSON/CONFIG ANALİZİ ===")
    
    # Yaygın config başlangıçları
    config_starts = [
        b"{",  # JSON
        b"[",  # JSON array
        b"client",  # OpenVPN
        b"remote",  # OpenVPN
        b"server",  # Config
        b"#",  # Comment
    ]
    
    for start_byte in config_starts:
        # Bu byte ile başlayan XOR anahtarlarını dene
        target_start = start_byte[0]
        
        for first_byte in data[:10]:  # İlk 10 byte'ı kontrol et
            xor_key = first_byte ^ target_start
            
            result = bytes([b ^ xor_key for b in data])
            
            if result.startswith(start_byte):
                print(f"Potansiyel config bulundu! XOR anahtar: {xor_key:02x}")
                
                try:
                    text = result.decode('utf-8', errors='ignore')
                    print(f"İçerik: {text[:300]}...")
                    
                    # JSON kontrolü
                    if start_byte == b"{" or start_byte == b"[":
                        try:
                            json_data = json.loads(text)
                            print("Geçerli JSON bulundu!")
                            
                            with open(f"vpn_json_{xor_key:02x}.json", "w", encoding="utf-8") as f:
                                json.dump(json_data, f, indent=2, ensure_ascii=False)
                            print(f"JSON dosyaya kaydedildi: vpn_json_{xor_key:02x}.json")
                            
                        except:
                            print("JSON parse edilemedi")
                    
                    # Config dosyası olarak kaydet
                    with open(f"vpn_config_{xor_key:02x}.ovpn", "w", encoding="utf-8") as f:
                        f.write(text)
                    print(f"Config dosyaya kaydedildi: vpn_config_{xor_key:02x}.ovpn")
                    
                except Exception as e:
                    print(f"Çözme hatası: {e}")

def main():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("HAPP VPN ŞİFRE ÇÖZME DENEMESİ")
    print("=" * 50)
    print(f"Şifreli veri uzunluğu: {len(encrypted_data)} bytes")
    
    # Farklı yöntemler dene
    result = try_common_vpn_keys(encrypted_data)
    
    if not result:
        try_vpn_config_patterns(encrypted_data)
        analyze_as_json_or_config(encrypted_data)
    
    print("\n" + "=" * 50)
    print("VPN config çözme denemesi tamamlandı.")
    print("Eğer başarılı olursa, .ovpn veya .json dosyaları oluşturulacak.")

if __name__ == "__main__":
    main()
