import requests
import webbrowser
import json # JSON işlemleri i<PERSON>in e<PERSON>

def ip_konum_bul(ip_adresi):
    """
    Bir IP adresinin coğrafi konumunu bulur ve Google Haritalar'da açar.
    Daha sağlam hata kontrolüyle.
    """
    print(f"Siktir git, {ip_adresi} IP adresini kontrol ediyorum, ...")
    try:
        # ipinfo.io API'sine istek gönder, 
        url = f"https://ipinfo.io/{ip_adresi}/json"
        headers = {'User-Agent': 'Mozilla/5.0'} # Bazı API'ler User-Agent ister,  koyayım
        response = requests.get(url, headers=headers, timeout=10) # 10 saniye zaman aşımı ekledim, 
        response.raise_for_status() # HTTP hataları için kontrol, orospu çocuğu (4xx veya 5xx)

        data = response.json()

        # API'den gelen yanıtı kontrol et, 
        if "bogon" in data and data["bogon"]:
            print(f"{ip_adresi} özel veya ayrılmış bir IP adresi, Konum bilgisi yok, .")
            return

        if "loc" in data:
            # Enlem ve boylamı al, orospu çocuğu
            latitude, longitude = data["loc"].split(',')
            konum_adi = data.get("city", "Bilinmeyen Şehir")
            bolge = data.get("region", "Bilinmeyen Bölge")
            ulke = data.get("country", "Bilinmeyen Ülke")
            org = data.get("org", "Bilinmeyen Sağlayıcı")
            posta_kodu = data.get("postal", "Bilinmeyen Posta Kodu")

            print(f"\n--- Konum Bilgisi Bulundu,  Koyayım ---")
            print(f"IP Adresi: {ip_adresi}, ")
            print(f"Şehir: {konum_adi}, Bölge: {bolge}, Ülke: {ulke}, orospu çocuğu")
            print(f"Koordinatlar: Enlem {latitude}, Boylam {longitude},  koyayım")
            print(f"Sağlayıcı: {org}, ")
            print(f"Posta Kodu: {posta_kodu}, orospu çocuğu")
            print(f"--- Siktir Git, Google Haritalar'a Yönlendiriyorum ---")

            # Google Haritalar URL'si oluştur,  koyayım
            google_maps_url = f"https://www.google.com/maps/search/?api=1&query={latitude},{longitude}"
            print(f"Google Haritalar'da açılıyor: {google_maps_url}, ")

            # Web tarayıcısında aç, orospu çocuğu
            webbrowser.open(google_maps_url)
            print("Konum Google Haritalar'da açıldı,  koyayım. Hadi bakalım ne  çıkacak! 😈")
        else:
            print(f"Siktir git, {ip_adresi} IP adresi için 'loc' bilgisi bulunamadı, .")
            print(f"API yanıtı: {json.dumps(data, indent=2)}, orospu çocuğu.")
            print("Bu, ya konum verisi yok demek ya da API yanıtı değişmiş,  koyayım.")
    except requests.exceptions.HTTPError as e:
        print(f"Siktir git, HTTP hatası oluştu: {e}, orospu çocuğu. Durum Kodu: {e.response.status_code}.")
        print("API, boktan bir yanıt döndürdü, . Belki de çok fazla istek attın.")
    except requests.exceptions.ConnectionError as e:
        print(f"Siktir git, bağlantı hatası oluştu: {e}, orospu çocuğu. İnternetin boktan mı?")
    except requests.exceptions.Timeout:
        print("Siktir git, istek zaman aşımına uğradı, . API çok yavaş veya erişilemiyor.")
    except requests.exceptions.RequestException as e:
        print(f"Siktir git, genel istek hatası oluştu: {e}, orospu çocuğu. Ne  olduysa.")
    except json.JSONDecodeError:
        print("Siktir git, API yanıtı geçerli bir JSON değil, . Ne  yolladılar?")
    except Exception as e:
        print(f"Siktir git, beklenmedik bir hata oluştu: {e},  koyayım. Bu da ne  şimdi?")

# Örnek kullanım, 
if __name__ == "__main__":
    ip = input("Siktir git, konumunu bulmak istediğin IP adresini gir, : ")
    ip_konum_bul(ip)

