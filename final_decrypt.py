#!/usr/bin/env python3
import base64
import hashlib

def try_custom_protocol_decrypt(data):
    """Özel protokol şifre çözme denemesi"""
    
    print("=== ÖZEL PROTOKOL ŞİFRE ÇÖZME ===")
    
    # "happ" kelimesinden anahtar türet
    keys_to_try = [
        b"happ",
        b"crypt2", 
        b"happcrypt2",
        hashlib.md5(b"happ").digest()[:16],
        hashlib.sha1(b"happ").digest()[:16],
        hashlib.md5(b"crypt2").digest()[:16],
    ]
    
    for i, key in enumerate(keys_to_try):
        print(f"\nAnahtar {i+1}: {key}")
        
        # XOR ile çöz
        result = bytes([data[j] ^ key[j % len(key)] for j in range(len(data))])
        
        # ASCII kontrolü
        ascii_count = sum(1 for b in result if 32 <= b <= 126)
        ascii_ratio = ascii_count / len(result)
        
        print(f"ASCII oranı: {ascii_ratio:.2%}")
        
        if ascii_ratio > 0.6:
            try:
                text = result.decode('utf-8', errors='ignore')
                print(f"Sonuç: {text[:200]}...")
                
                with open(f"custom_decrypt_{i+1}.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                print(f"Dosyaya kaydedildi: custom_decrypt_{i+1}.txt")
                
            except Exception as e:
                print(f"UTF-8 çözme hatası: {e}")

def try_block_cipher_patterns(data):
    """Blok şifre desenlerini dene"""
    
    print("\n=== BLOK ŞİFRE DESENLERİ ===")
    
    # 16 byte bloklar halinde analiz (AES)
    if len(data) % 16 == 0:
        print("16 byte bloklar - AES olabilir")
        blocks = [data[i:i+16] for i in range(0, len(data), 16)]
        
        # Blok tekrarları kontrol et
        unique_blocks = set(blocks)
        print(f"Toplam blok: {len(blocks)}, Benzersiz blok: {len(unique_blocks)}")
        
        if len(unique_blocks) < len(blocks):
            print("Tekrarlayan bloklar var - ECB modu olabilir")
    
    # 8 byte bloklar (DES)
    if len(data) % 8 == 0:
        print("8 byte bloklar - DES olabilir")

def try_frequency_analysis(data):
    """Frekans analizi ile şifre çözme"""
    
    print("\n=== FREKANS ANALİZİ ===")
    
    # En yaygın byte'ı boşluk (32) olarak varsay
    freq = {}
    for byte in data:
        freq[byte] = freq.get(byte, 0) + 1
    
    most_common = max(freq.items(), key=lambda x: x[1])
    space_key = most_common[0] ^ 32  # Boşluk karakteri
    
    print(f"En yaygın byte: {most_common[0]:02x} ({most_common[1]} kez)")
    print(f"Boşluk varsayımı ile anahtar: {space_key:02x}")
    
    # Bu anahtarla çöz
    result = bytes([b ^ space_key for b in data])
    ascii_count = sum(1 for b in result if 32 <= b <= 126)
    ascii_ratio = ascii_count / len(result)
    
    print(f"ASCII oranı: {ascii_ratio:.2%}")
    
    if ascii_ratio > 0.5:
        try:
            text = result.decode('utf-8', errors='ignore')
            print(f"Sonuç: {text[:200]}...")
            
            with open("frequency_decrypt.txt", "w", encoding="utf-8") as f:
                f.write(text)
            print("Dosyaya kaydedildi: frequency_decrypt.txt")
            
        except Exception as e:
            print(f"UTF-8 çözme hatası: {e}")

def try_multi_byte_xor(data):
    """Çok byte'lık XOR anahtarları dene"""
    
    print("\n=== ÇOK BYTE XOR ===")
    
    # 2-8 byte arası anahtarlar dene
    for key_len in range(2, 9):
        print(f"\n{key_len} byte anahtar deneniyor...")
        
        best_score = 0
        best_key = None
        best_result = None
        
        # Brute force (sadece küçük anahtarlar için)
        if key_len <= 3:
            for key_val in range(2**(key_len*8)):
                key = key_val.to_bytes(key_len, 'big')
                result = bytes([data[i] ^ key[i % key_len] for i in range(len(data))])
                
                # Basit skor hesapla (ASCII karakter sayısı)
                score = sum(1 for b in result if 32 <= b <= 126)
                
                if score > best_score:
                    best_score = score
                    best_key = key
                    best_result = result
        
        if best_key:
            ascii_ratio = best_score / len(data)
            print(f"En iyi anahtar: {best_key.hex()} (ASCII: {ascii_ratio:.2%})")
            
            if ascii_ratio > 0.6:
                try:
                    text = best_result.decode('utf-8', errors='ignore')
                    print(f"Sonuç: {text[:200]}...")
                    
                    with open(f"multibyte_xor_{key_len}.txt", "w", encoding="utf-8") as f:
                        f.write(text)
                    print(f"Dosyaya kaydedildi: multibyte_xor_{key_len}.txt")
                    
                except Exception as e:
                    print(f"UTF-8 çözme hatası: {e}")

def main():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    decoded = base64.b64decode(clean_data)
    
    print(f"Çözülen veri uzunluğu: {len(decoded)} bytes")
    
    # Farklı yöntemler dene
    try_custom_protocol_decrypt(decoded)
    try_block_cipher_patterns(decoded)
    try_frequency_analysis(decoded)
    try_multi_byte_xor(decoded)
    
    print("\n=== ÖZET ===")
    print("Tüm yöntemler denendi. Eğer hiçbiri başarılı olmadıysa,")
    print("bu muhtemelen AES, RSA gibi güçlü bir şifreleme algoritması")
    print("ve doğru anahtara ihtiyaç duyuyor.")

if __name__ == "__main__":
    main()
