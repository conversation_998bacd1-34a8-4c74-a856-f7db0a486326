#!/usr/bin/env python3
import base64
import hashlib

def test_sha256_key():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    # En iyi anahtar: SHA256 hash of "happvpn"
    key = hashlib.sha256(b"happvpn").digest()[:16]
    print(f"Anahtar (hex): {key.hex()}")
    print(f"Anahtar (bytes): {key}")
    
    # XOR ile çöz
    result = bytes([encrypted_data[i] ^ key[i % len(key)] for i in range(len(encrypted_data))])
    
    # ASCII kontrolü
    ascii_count = sum(1 for b in result if 32 <= b <= 126)
    ascii_ratio = ascii_count / len(result)
    print(f"ASCII oranı: {ascii_ratio:.2%}")
    
    # İlk 200 karakteri göster
    try:
        text = result.decode('utf-8', errors='ignore')
        print(f"\nÇözülen metin (ilk 200 karakter):")
        print(text[:200])
        print("\n" + "="*50)
        
        # Tüm metni göster
        print("TAMAMLANMIŞ METİN:")
        print(text)
        
        # Dosyaya kaydet
        with open("final_vpn_config.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print(f"\nDosyaya kaydedildi: final_vpn_config.txt")
        
        # VPN config olup olmadığını kontrol et
        vpn_keywords = ["client", "remote", "port", "proto", "dev", "ca", "cert", "key", "cipher"]
        found_keywords = [kw for kw in vpn_keywords if kw in text.lower()]
        
        if found_keywords:
            print(f"\nVPN anahtar kelimeleri bulundu: {found_keywords}")
            print("Bu muhtemelen bir VPN config dosyası!")
            
            # .ovpn olarak da kaydet
            with open("final_vpn_config.ovpn", "w", encoding="utf-8") as f:
                f.write(text)
            print("OpenVPN config olarak kaydedildi: final_vpn_config.ovpn")
        
    except Exception as e:
        print(f"UTF-8 çözme hatası: {e}")
        
        # Binary olarak kaydet
        with open("final_vpn_config.bin", "wb") as f:
            f.write(result)
        print("Binary olarak kaydedildi: final_vpn_config.bin")
    
    # Hex dump (ilk 128 byte)
    print(f"\nHex dump (ilk 128 byte):")
    for i in range(0, min(128, len(result)), 16):
        hex_part = " ".join(f"{b:02x}" for b in result[i:i+16])
        ascii_part = "".join(chr(b) if 32 <= b <= 126 else "." for b in result[i:i+16])
        print(f"{i:04x}: {hex_part:<48} {ascii_part}")

if __name__ == "__main__":
    test_sha256_key()
