#!/usr/bin/env python3
import base64
import hashlib
import re
import json
from urllib.parse import unquote, parse_qs

def extract_vless_shadowsocks():
    """VLESS ve Shadowsocks kodlarını çıkar"""
    
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("🔍 VLESS & SHADOWSOCKS KOD ÇIKARICI")
    print("=" * 60)
    
    # Çok geniş anahtar listesi
    all_keys = [
        # Happ VPN özel
        "happvpn", "HAPPVPN", "HappVPN", "happ_vpn", "Happ_VPN",
        "crypto.happ.su", "happ.su", "crypto", "happ", "crypt2",
        "happ://crypt2/", "happcrypt2", "HAPPCRYPT2",
        
        # VPN terimleri
        "vless", "VLESS", "shadowsocks", "SHADOWSOCKS", "ss", "SS",
        "vmess", "VMESS", "trojan", "TROJAN", "v2ray", "V2RAY",
        
        # Yaygın şifreler
        "password", "PASSWORD", "secret", "SECRET", "key", "KEY",
        "admin", "ADMIN", "user", "USER", "default", "DEFAULT",
        "123456", "password123", "admin123", "secret123",
        
        # Domain/Site tabanlı
        "vpn", "VPN", "proxy", "PROXY", "tunnel", "TUNNEL",
        "config", "CONFIG", "server", "SERVER",
        
        # Türkçe
        "sifre", "SIFRE", "anahtar", "ANAHTAR", "gizli", "GIZLI",
        "parola", "PAROLA", "vpn123", "happ123", "crypto123"
    ]
    
    found_configs = []
    
    for key_str in all_keys:
        # Farklı hash yöntemleri
        key_variants = [
            key_str.encode('utf-8'),
            hashlib.md5(key_str.encode()).digest(),
            hashlib.sha1(key_str.encode()).digest(),
            hashlib.sha256(key_str.encode()).digest(),
            hashlib.sha512(key_str.encode()).digest(),
            # Farklı uzunluklar
            hashlib.md5(key_str.encode()).digest()[:8],
            hashlib.md5(key_str.encode()).digest()[:16],
            hashlib.sha1(key_str.encode()).digest()[:16],
            hashlib.sha1(key_str.encode()).digest()[:20],
            hashlib.sha256(key_str.encode()).digest()[:16],
            hashlib.sha256(key_str.encode()).digest()[:24],
            hashlib.sha256(key_str.encode()).digest()[:32],
        ]
        
        for key in key_variants:
            try:
                # XOR ile çöz
                result = bytes([encrypted_data[i] ^ key[i % len(key)] for i in range(len(encrypted_data))])
                
                try:
                    text = result.decode('utf-8', errors='ignore')
                    
                    # VLESS kodlarını ara
                    vless_pattern = r'vless://[A-Za-z0-9+/=\-_@:.?&%#]+'
                    vless_matches = re.findall(vless_pattern, text, re.IGNORECASE)
                    
                    # Shadowsocks kodlarını ara
                    ss_pattern = r'ss://[A-Za-z0-9+/=\-_@:.?&%#]+'
                    ss_matches = re.findall(ss_pattern, text, re.IGNORECASE)
                    
                    # VMess kodlarını ara
                    vmess_pattern = r'vmess://[A-Za-z0-9+/=\-_@:.?&%#]+'
                    vmess_matches = re.findall(vmess_pattern, text, re.IGNORECASE)
                    
                    # Trojan kodlarını ara
                    trojan_pattern = r'trojan://[A-Za-z0-9+/=\-_@:.?&%#]+'
                    trojan_matches = re.findall(trojan_pattern, text, re.IGNORECASE)
                    
                    # JSON config ara
                    json_configs = []
                    if '{' in text and '}' in text:
                        try:
                            # JSON parçalarını bul
                            json_start = text.find('{')
                            json_end = text.rfind('}') + 1
                            if json_start != -1 and json_end > json_start:
                                json_text = text[json_start:json_end]
                                json_data = json.loads(json_text)
                                json_configs.append(json_data)
                        except:
                            pass
                    
                    # Sonuçları topla
                    if vless_matches or ss_matches or vmess_matches or trojan_matches or json_configs:
                        config_info = {
                            'key': key_str,
                            'vless': vless_matches,
                            'shadowsocks': ss_matches,
                            'vmess': vmess_matches,
                            'trojan': trojan_matches,
                            'json_configs': json_configs,
                            'full_text': text
                        }
                        found_configs.append(config_info)
                        
                        print(f"✅ BAŞARILI! Anahtar: {key_str}")
                        if vless_matches:
                            print(f"   🔵 VLESS: {len(vless_matches)} kod bulundu")
                        if ss_matches:
                            print(f"   🟡 Shadowsocks: {len(ss_matches)} kod bulundu")
                        if vmess_matches:
                            print(f"   🟢 VMess: {len(vmess_matches)} kod bulundu")
                        if trojan_matches:
                            print(f"   🔴 Trojan: {len(trojan_matches)} kod bulundu")
                        if json_configs:
                            print(f"   📋 JSON Config: {len(json_configs)} bulundu")
                        
                except:
                    continue
                    
            except:
                continue
    
    # Sonuçları kaydet
    if found_configs:
        print(f"\n{'='*60}")
        print(f"🎉 TOPLAM {len(found_configs)} CONFIG BULUNDU!")
        print("="*60)
        
        all_vless = []
        all_ss = []
        all_vmess = []
        all_trojan = []
        all_json = []
        
        for i, config in enumerate(found_configs):
            print(f"\n📦 CONFIG {i+1} (Anahtar: {config['key']}):")
            
            # VLESS kodları
            if config['vless']:
                print(f"🔵 VLESS Kodları ({len(config['vless'])}):")
                for j, vless in enumerate(config['vless']):
                    print(f"   {j+1}. {vless}")
                    all_vless.append(vless)
            
            # Shadowsocks kodları
            if config['shadowsocks']:
                print(f"🟡 Shadowsocks Kodları ({len(config['shadowsocks'])}):")
                for j, ss in enumerate(config['shadowsocks']):
                    print(f"   {j+1}. {ss}")
                    all_ss.append(ss)
            
            # VMess kodları
            if config['vmess']:
                print(f"🟢 VMess Kodları ({len(config['vmess'])}):")
                for j, vmess in enumerate(config['vmess']):
                    print(f"   {j+1}. {vmess}")
                    all_vmess.append(vmess)
            
            # Trojan kodları
            if config['trojan']:
                print(f"🔴 Trojan Kodları ({len(config['trojan'])}):")
                for j, trojan in enumerate(config['trojan']):
                    print(f"   {j+1}. {trojan}")
                    all_trojan.append(trojan)
            
            # JSON configs
            if config['json_configs']:
                print(f"📋 JSON Configs ({len(config['json_configs'])}):")
                for j, json_config in enumerate(config['json_configs']):
                    print(f"   {j+1}. {json.dumps(json_config, indent=2, ensure_ascii=False)[:200]}...")
                    all_json.append(json_config)
        
        # Tüm kodları dosyalara kaydet
        if all_vless:
            with open("VLESS_CODES.txt", "w", encoding="utf-8") as f:
                f.write("=== VLESS VPN KODLARI ===\n\n")
                for i, vless in enumerate(all_vless):
                    f.write(f"{i+1}. {vless}\n\n")
            print(f"\n💾 {len(all_vless)} VLESS kodu kaydedildi: VLESS_CODES.txt")
        
        if all_ss:
            with open("SHADOWSOCKS_CODES.txt", "w", encoding="utf-8") as f:
                f.write("=== SHADOWSOCKS VPN KODLARI ===\n\n")
                for i, ss in enumerate(all_ss):
                    f.write(f"{i+1}. {ss}\n\n")
            print(f"💾 {len(all_ss)} Shadowsocks kodu kaydedildi: SHADOWSOCKS_CODES.txt")
        
        if all_vmess:
            with open("VMESS_CODES.txt", "w", encoding="utf-8") as f:
                f.write("=== VMESS VPN KODLARI ===\n\n")
                for i, vmess in enumerate(all_vmess):
                    f.write(f"{i+1}. {vmess}\n\n")
            print(f"💾 {len(all_vmess)} VMess kodu kaydedildi: VMESS_CODES.txt")
        
        if all_trojan:
            with open("TROJAN_CODES.txt", "w", encoding="utf-8") as f:
                f.write("=== TROJAN VPN KODLARI ===\n\n")
                for i, trojan in enumerate(all_trojan):
                    f.write(f"{i+1}. {trojan}\n\n")
            print(f"💾 {len(all_trojan)} Trojan kodu kaydedildi: TROJAN_CODES.txt")
        
        if all_json:
            with open("JSON_CONFIGS.json", "w", encoding="utf-8") as f:
                json.dump(all_json, f, indent=2, ensure_ascii=False)
            print(f"💾 {len(all_json)} JSON config kaydedildi: JSON_CONFIGS.json")
        
        # Hepsini tek dosyada topla
        with open("ALL_VPN_CODES.txt", "w", encoding="utf-8") as f:
            f.write("🔐 HAPP VPN ŞİFRE ÇÖZME SONUCU - TÜM VPN KODLARI\n")
            f.write("=" * 60 + "\n\n")
            
            if all_vless:
                f.write("🔵 VLESS KODLARI:\n")
                f.write("-" * 30 + "\n")
                for i, vless in enumerate(all_vless):
                    f.write(f"{i+1}. {vless}\n")
                f.write("\n")
            
            if all_ss:
                f.write("🟡 SHADOWSOCKS KODLARI:\n")
                f.write("-" * 30 + "\n")
                for i, ss in enumerate(all_ss):
                    f.write(f"{i+1}. {ss}\n")
                f.write("\n")
            
            if all_vmess:
                f.write("🟢 VMESS KODLARI:\n")
                f.write("-" * 30 + "\n")
                for i, vmess in enumerate(all_vmess):
                    f.write(f"{i+1}. {vmess}\n")
                f.write("\n")
            
            if all_trojan:
                f.write("🔴 TROJAN KODLARI:\n")
                f.write("-" * 30 + "\n")
                for i, trojan in enumerate(all_trojan):
                    f.write(f"{i+1}. {trojan}\n")
                f.write("\n")
        
        print(f"\n🎯 TÜM KODLAR TOPLANDI: ALL_VPN_CODES.txt")
        
    else:
        print("❌ Maalesef VLESS veya Shadowsocks kodu bulunamadı.")
        print("Şifre henüz tam olarak çözülememiş olabilir.")

if __name__ == "__main__":
    extract_vless_shadowsocks()
