#!/usr/bin/env python3
import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import binascii

def try_rsa_decrypt_with_key(encrypted_data, private_key_pem):
    """RSA private key ile şifre ç<PERSON>z<PERSON>"""
    try:
        # Private key'i yükle
        private_key = RSA.import_key(private_key_pem)
        cipher = PKCS1_OAEP.new(private_key)
        
        # <PERSON><PERSON><PERSON><PERSON>
        decrypted = cipher.decrypt(encrypted_data)
        
        return decrypted.decode('utf-8')
    except Exception as e:
        print(f"RSA çözme hatası: {e}")
        return None

def try_rsa_without_key(encrypted_data):
    """RSA anahtarı olmadan deneme (zayıf RSA için)"""
    
    print("=== RSA ANALİZİ ===")
    print(f"Şifreli veri uzunluğu: {len(encrypted_data)} bytes")
    
    # RSA-1024 = 128 bytes, RSA-2048 = 256 bytes, RSA-4096 = 512 bytes
    if len(encrypted_data) == 128:
        print("RSA-1024 olabilir (128 bytes)")
    elif len(encrypted_data) == 256:
        print("RSA-2048 olabilir (256 bytes)")
    elif len(encrypted_data) == 512:
        print("RSA-4096 olabilir (512 bytes)")
    else:
        print(f"Standart RSA boyutu değil: {len(encrypted_data)} bytes")
    
    # Hex dump
    print("\nHex dump (ilk 64 byte):")
    for i in range(0, min(64, len(encrypted_data)), 16):
        hex_part = " ".join(f"{b:02x}" for b in encrypted_data[i:i+16])
        print(f"{i:04x}: {hex_part}")

def main():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("RSA ŞİFRE ÇÖZME DENEMESİ")
    print("=" * 40)
    
    # RSA analizi
    try_rsa_without_key(encrypted_data)
    
    print("\n" + "=" * 40)
    print("RSA şifresini çözmek için aşağıdakilerden birine ihtiyaç var:")
    print("1. RSA Private Key (PEM formatında)")
    print("2. RSA şifreleme sitesinin private key'i")
    print("3. Zayıf RSA parametreleri (factorization)")
    
    # Eğer private key varsa buraya ekleyin
    private_key_example = """
    -----BEGIN RSA PRIVATE KEY-----
    [BURAYA PRIVATE KEY GELİR]
    -----END RSA PRIVATE KEY-----
    """
    
    print("\nPrivate key'iniz varsa, yukarıdaki private_key_example değişkenine")
    print("yapıştırın ve kodu tekrar çalıştırın.")

if __name__ == "__main__":
    main()
