2025-02-08 00:53:01,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 00:53:01,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 00:53:01,615 - telegram.ext.Application - INFO - Application started
2025-02-08 00:53:11,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:11,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 00:53:11,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:14,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:14,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:16,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:16,480 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:17,989 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:20,231 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:26,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:26,819 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:35,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:35,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 00:53:35,472 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 00:53:45,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:53:55,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:05,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:15,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:25,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:35,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:45,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:54:56,072 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:06,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:16,333 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:26,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:36,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:46,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:55:56,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:07,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:17,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:27,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:37,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:47,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:56:57,702 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:07,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:17,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:28,087 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:38,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:48,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:57:58,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:08,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:18,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:28,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:39,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:49,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:58:59,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:59:09,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:59:19,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:59:29,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:59:39,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 00:59:54,968 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 00:59:55,098 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 00:59:55,099 - telegram.ext.Application - INFO - Application started
2025-02-08 01:00:01,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:01,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:00:01,529 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:00:11,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:11,886 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:12,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:00:12,352 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:00:22,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:31,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:32,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:00:32,285 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:00:41,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:00:52,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:02,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:12,293 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:22,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:30,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 01:01:30,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 01:01:30,218 - telegram.ext.Application - INFO - Application started
2025-02-08 01:01:34,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:35,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:01:35,014 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:01:38,796 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:39,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:01:39,010 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:01:40,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:41,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:01:50,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:01:51,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:01:51,107 - root - ERROR - Hava durumu hatası: name 'requests' is not defined
2025-02-08 01:02:00,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:10,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:11,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:11,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:12,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:12,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:16,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:16,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:18,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:18,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:02:18,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:20,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:21,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:31,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:39,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:39,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:39,281 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): None
2025-02-08 01:02:48,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:48,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:02:51,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:02:51,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:03:00,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:01,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/banChatMember "HTTP/1.1 400 Bad Request"
2025-02-08 01:03:01,512 - root - ERROR - Hata: Chat_admin_required
2025-02-08 01:03:11,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:13,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:14,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:03:23,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:33,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:44,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:03:54,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:04,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:14,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:24,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:34,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:44,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:04:55,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:05,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:15,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:25,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:35,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:45,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:05:55,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:06:05,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:06:16,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:06:26,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:12,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 01:07:13,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 01:07:13,098 - telegram.ext.Application - INFO - Application started
2025-02-08 01:07:15,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:15,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:07:18,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:25,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:25,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:07:27,697 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:27,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:07:33,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:35,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:07:44,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:45,198 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:07:45,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:08:04,188 - telegram.ext.Updater - ERROR - Error while getting Updates: Unknown error in HTTP implementation: EndOfStream()
2025-02-08 01:08:04,188 - root - ERROR - Hata: Unknown error in HTTP implementation: EndOfStream()
2025-02-08 01:08:07,042 - telegram.ext.Updater - ERROR - Error while getting Updates: Unknown error in HTTP implementation: EndOfStream()
2025-02-08 01:08:07,042 - root - ERROR - Hata: Unknown error in HTTP implementation: EndOfStream()
2025-02-08 01:08:14,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:08:20,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:08:20,620 - root - ERROR - Hava durumu hatası: Timed out
2025-02-08 01:08:25,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:08:35,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:08:45,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:08:55,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:05,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:16,176 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:22,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:22,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:09:23,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:09:27,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:27,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:09:37,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:47,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:09:57,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:07,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:17,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:27,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:32,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:32,753 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/banChatMember "HTTP/1.1 200 OK"
2025-02-08 01:10:32,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:10:42,384 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:10:52,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:02,662 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:12,800 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:23,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:33,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:43,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:50,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:51,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:11:52,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:52,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:11:54,437 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:11:54,606 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:12:00,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:12:01,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:12:11,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:12:21,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:12:31,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:12:41,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:12:51,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:01,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:11,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:22,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:32,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:42,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:13:52,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:02,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:12,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:22,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:33,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:43,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:14:53,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:15:03,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:15:13,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:15:23,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:15:34,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:15:56,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:06,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:16,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:26,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:37,063 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:47,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:16:57,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:07,446 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:17,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:27,701 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:37,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:47,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:17:58,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:08,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:18,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:28,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:38,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:48,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:18:58,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:08,976 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:21,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 01:19:21,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 01:19:21,956 - telegram.ext.Application - INFO - Application started
2025-02-08 01:19:32,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:41,990 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:42,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:19:52,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:55,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:19:55,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:20:05,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:20:15,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:20:25,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:20:36,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:20:46,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:20:56,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:06,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:16,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:26,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:36,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:47,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:21:57,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:07,389 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:17,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:27,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:37,798 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:47,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:22:58,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:08,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:12,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:12,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:23:15,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:16,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:23:19,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:19,259 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:23:22,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:23,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:23:33,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:43,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:23:53,366 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:03,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:13,699 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:23,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:33,981 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:44,117 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:52,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:24:53,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:25:02,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:25:12,858 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:25:23,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:25:33,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:25:43,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:25:53,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:03,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:13,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:23,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:34,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:44,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:26:54,282 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:02,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:03,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:27:12,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:15,366 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:16,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:27:25,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:35,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:46,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:27:56,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:06,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:16,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:26,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:36,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:46,933 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:28:57,078 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:07,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:17,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:27,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:37,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:47,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:29:57,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:08,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:18,222 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:28,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:38,506 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:48,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:30:58,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:08,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:19,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:29,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:39,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:49,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:31:59,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:09,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:19,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:30,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:33,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:43,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:44,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:32:44,926 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /unban
2025-02-08 01:32:55,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:33:05,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:33:15,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:34:37,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 01:34:37,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 01:34:37,536 - telegram.ext.Application - INFO - Application started
2025-02-08 01:34:47,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:34:56,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:34:57,401 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:34:59,099 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:34:59,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:04,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:04,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:35:05,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:08,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:08,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:14,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:15,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:25,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:33,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:34,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:42,041 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:35:42,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:35:50,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:00,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:00,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:01,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/unbanChatMember "HTTP/1.1 200 OK"
2025-02-08 01:36:01,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:36:09,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:09,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/unbanChatMember "HTTP/1.1 200 OK"
2025-02-08 01:36:09,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:36:19,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:29,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:39,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:49,819 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:58,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:36:58,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:00,594 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:00,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:09,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:09,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:16,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:16,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:37:17,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:22,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:22,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:32,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:39,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:40,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:37:50,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:37:59,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:00,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:38:09,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:11,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:12,629 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:38:22,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:32,425 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:42,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:38:52,701 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:02,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:12,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:23,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:25,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:25,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:39:25,947 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:39:35,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:45,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:39:55,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:05,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:15,996 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:26,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:36,276 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:46,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:40:56,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:06,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:16,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:26,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:37,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:47,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:41:57,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:07,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:17,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:27,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:37,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:48,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:42:58,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:08,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:18,476 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:28,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:38,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:48,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:43:59,067 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:09,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:19,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:28,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:29,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:44:35,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:45,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:44:55,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:06,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:16,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:26,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:36,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:46,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:45:56,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:06,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:16,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:21,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:21,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:21,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:21,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:22,110 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:22,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:22,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:22,606 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:32,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:40,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:41,550 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:46:50,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:51,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 400 Bad Request"
2025-02-08 01:46:51,435 - root - ERROR - Hata: Message to be replied not found
2025-02-08 01:46:57,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:46:58,365 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 400 Bad Request"
2025-02-08 01:46:58,366 - root - ERROR - Hata: Message to be replied not found
2025-02-08 01:47:04,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:05,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:14,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:15,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:16,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:17,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:17,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:21,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:21,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:24,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:24,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:47:25,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:28,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:28,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:38,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:39,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:40,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:45,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:46,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:51,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:47:51,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:47:57,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:06,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:16,701 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:26,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:36,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:47,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:47,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:49,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:53,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:53,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:48:55,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:48:58,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:08,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:19,098 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:29,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:39,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:45,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:49,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:49:59,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:05,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:15,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:25,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:35,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:46,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:50:56,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:06,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:16,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:19,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:19,802 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:23,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:23,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:28,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:29,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:38,781 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:45,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:46,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:48,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:49,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 01:51:49,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:51,099 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:51,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:54,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:54,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:51:57,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:51:57,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:52:07,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:52:17,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:52:27,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:52:38,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:52:48,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:52:58,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:08,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:18,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:28,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:38,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:49,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:49,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:49,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:53:49,793 - root - INFO - Yeni mesaj: #𝐆𝐮𝐠𝐚 𝐔𝐜 𝐒𝐡𝐨𝐩𝐬 (8046165511): None
2025-02-08 01:53:59,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:05,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:05,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:54:15,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:16,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:16,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:54:24,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:25,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:54:34,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:45,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:49,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:50,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/banChatMember "HTTP/1.1 200 OK"
2025-02-08 01:54:50,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:50,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/unbanChatMember "HTTP/1.1 200 OK"
2025-02-08 01:54:50,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:54:50,889 - root - INFO - Yeni mesaj: MyPrivateHelperBot (7915145533): None
2025-02-08 01:54:58,886 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:54:59,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:55:09,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:15,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:15,912 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /warn@GHClone5Bot
2025-02-08 01:55:26,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:36,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:42,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:42,091 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /unban@GHClone5Bot
2025-02-08 01:55:52,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:52,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:55:53,146 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:56:01,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:01,981 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /info@GHClone5Bot
2025-02-08 01:56:12,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:12,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:12,319 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /info
2025-02-08 01:56:22,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:32,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:42,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:52,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:58,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:56:58,094 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /reload@GHClone5Bot
2025-02-08 01:57:01,931 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:57:01,932 - root - INFO - Yeni mesaj: • 𝑯𝒂𝒓𝒅𝒄𝒐𝒓𝒆 • 𝑩𝒆𝒏𝒋𝒂𝒎𝒊𝒏 (5421243466): /reload
2025-02-08 01:57:12,068 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:57:22,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:57:32,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:57:42,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:57:52,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:02,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:12,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:23,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:33,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:43,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:58:53,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:03,637 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:13,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:23,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:34,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:44,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:46,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:47,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:59:49,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:49,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:59:51,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:51,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:59:54,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:54,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 01:59:56,671 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 01:59:56,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 02:00:01,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 02:00:01,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 02:00:03,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 02:00:04,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 02:00:05,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 02:00:05,899 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 02:00:06,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 02:00:07,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 02:00:08,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 02:00:08,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 02:00:08,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:20,930 - __main__ - INFO - Bot başlatılıyor...
2025-02-08 10:29:21,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 10:29:21,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 10:29:21,742 - telegram.ext.Application - INFO - Application started
2025-02-08 10:29:22,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:29:22,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:22,496 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:22,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 403 Forbidden"
2025-02-08 10:29:22,644 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\bot.py", line 57, in start
    await update.message.reply_text(
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 316, in _request_wrapper
    raise Forbidden(message)
telegram.error.Forbidden: Forbidden: bot was blocked by the user
2025-02-08 10:29:22,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 403 Forbidden"
2025-02-08 10:29:22,850 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\bot.py", line 57, in start
    await update.message.reply_text(
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 316, in _request_wrapper
    raise Forbidden(message)
telegram.error.Forbidden: Forbidden: bot was blocked by the user
2025-02-08 10:29:23,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 403 Forbidden"
2025-02-08 10:29:23,002 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\bot.py", line 57, in start
    await update.message.reply_text(
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 316, in _request_wrapper
    raise Forbidden(message)
telegram.error.Forbidden: Forbidden: bot was blocked by the user
2025-02-08 10:29:23,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 403 Forbidden"
2025-02-08 10:29:23,151 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\bot.py", line 57, in start
    await update.message.reply_text(
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 316, in _request_wrapper
    raise Forbidden(message)
telegram.error.Forbidden: Forbidden: bot was blocked by the user
2025-02-08 10:29:23,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:31,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:29:32,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:33,933 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:29:34,108 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:35,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:29:36,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:29:46,010 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:29:56,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:06,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:17,032 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:25,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:26,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:30:30,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:30,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:30:32,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:32,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:30:35,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:44,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:30:54,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:04,904 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:15,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:25,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:28,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:38,424 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:41,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:31:42,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:31:51,696 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:01,832 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:11,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:22,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:32,240 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:42,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:49,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:49,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:32:52,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:52,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:32:56,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:56,527 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:32:58,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:32:58,720 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:33:08,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:18,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:28,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:32,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:32,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:33:42,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:53,052 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:54,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:54,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:33:55,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:33:56,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:34:06,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:16,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:26,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:28,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:28,832 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:34:34,016 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:34,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:34:39,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:39,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:47,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:34:47,993 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:34:54,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:04,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:08,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:08,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:35:09,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:09,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:35:10,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:10,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:35:16,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:17,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:35:26,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:37,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:46,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:35:47,039 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 10:35:47,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:35:55,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:05,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:15,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:25,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:36,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:36,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:36,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 10:36:36,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:36:44,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:45,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:36:45,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 10:36:46,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 10:36:55,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:05,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:15,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:26,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:36,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:46,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:37:56,494 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:06,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:16,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:26,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:37,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:47,180 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:38:57,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:07,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:17,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:27,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:37,858 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:48,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:39:58,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:08,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:18,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:28,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:38,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:48,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:40:59,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:41:09,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:41:19,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:41:29,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:41:39,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:41:49,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:00,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:10,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:20,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:30,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:40,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:42:51,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:01,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:11,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:21,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:31,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:42,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:43:52,240 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:02,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:12,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:22,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:32,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:42,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:44:53,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:03,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:13,365 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:23,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:33,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:43,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:45:53,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:04,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:14,256 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:24,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:34,609 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:44,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:46:54,912 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:05,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:15,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:25,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:35,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:46,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:47:56,300 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:06,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:16,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:26,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:36,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:47,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:48:57,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:07,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:17,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:27,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:37,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:47,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:49:58,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:08,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:18,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:28,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:38,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:49,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:50:59,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:51:09,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:51:19,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:51:29,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:51:39,753 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:51:49,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:00,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:10,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:20,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:30,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:40,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:52:50,715 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:00,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:11,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:21,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:31,285 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:41,424 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:53:51,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:01,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:11,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:21,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:32,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:42,480 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:54:52,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:02,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:12,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:23,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:33,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:43,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:55:53,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:03,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:13,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:24,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:34,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:44,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:56:54,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:04,625 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:14,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:24,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:35,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:45,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:49,808 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:57:59,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:58:10,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:58:20,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:58:30,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:58:40,494 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:58:50,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:00,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:10,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:21,065 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:31,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:41,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 10:59:51,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:02,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:12,149 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:22,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:32,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:42,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:00:52,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:02,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:13,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:23,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:33,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:43,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:01:53,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:03,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:13,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:24,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:34,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:44,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:02:54,550 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:04,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:14,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:25,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:35,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:45,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:03:55,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:05,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:15,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:26,016 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:36,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:46,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:04:56,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:06,693 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:16,824 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:26,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:37,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:47,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:05:57,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:07,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:17,692 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:27,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:37,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:48,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:06:58,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:08,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:18,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:28,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:38,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:48,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:07:59,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:09,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:19,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:29,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:39,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:49,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:08:59,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:09:10,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:09:20,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:09:30,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:09:41,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:09:51,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:02,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:12,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:23,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:33,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:43,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:10:54,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:04,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:14,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:25,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:35,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:45,796 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:11:56,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:12:07,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:12:17,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:12:27,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:12:51,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:02,134 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:12,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:23,075 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:33,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:44,573 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:13:55,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:05,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:15,696 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:16,822 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:17,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:14:23,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:29,138 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\streams\tls.py", line 140, in _call_sslobject_method
    result = func(*args)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 979, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLWantReadError: The operation did not complete (read) (_ssl.c:1006)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\_core\_tasks.py", line 115, in fail_after
    yield cancel_scope
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 69, in start_tls
    ssl_stream = await anyio.streams.tls.TLSStream.wrap(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\streams\tls.py", line 132, in wrap
    await wrapper._call_sslobject_method(ssl_object.do_handshake)
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\streams\tls.py", line 147, in _call_sslobject_method
    data = await self.transport_stream.receive()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 1246, in receive
    await self._protocol.read_event.wait()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\locks.py", line 213, in wait
    await fut
asyncio.exceptions.CancelledError: Cancelled by cancel scope 29141431450

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 78, in start_tls
    raise exc
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 68, in start_tls
    with anyio.fail_after(timeout):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\anyio\_core\_tasks.py", line 118, in fail_after
    raise TimeoutError
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_async\connection.py", line 149, in _connect
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 66, in start_tls
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\bot.py", line 79, in help
    await update.message.reply_text(help_text)
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\gmail acc\venv\Lib\site-packages\telegram\request\_httpxrequest.py", line 236, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out
2025-02-08 11:14:34,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:35,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:36,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:14:40,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:14:41,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:14:51,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:01,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:12,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:16,300 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:21,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:15:29,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:39,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:15:50,108 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:00,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:10,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:20,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:30,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:40,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:16:51,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:01,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:11,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:21,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:31,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:41,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:17:51,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:02,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:12,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:22,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:32,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:42,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:47,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:18:48,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:18:57,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:07,701 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:17,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:27,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:38,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:48,240 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:19:58,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:07,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:07,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 11:20:17,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:27,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:37,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:47,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:20:58,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:08,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:18,348 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:28,483 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:38,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:48,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:21:58,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:09,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:19,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:29,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:39,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:49,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:22:59,697 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:23:09,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:23:19,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:23:30,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:23:40,242 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:23:50,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:00,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:10,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:20,858 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:31,016 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:41,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:24:51,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:01,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:11,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:21,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:31,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:41,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:25:52,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:02,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:12,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:22,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:32,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:42,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:26:52,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:03,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:13,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:23,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:33,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:43,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:27:53,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:03,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:14,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:24,252 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:34,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:44,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:28:54,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:04,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:15,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:25,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:35,619 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:45,760 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:29:55,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:06,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:16,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:26,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:36,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:46,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:30:56,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:06,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:17,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:27,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:37,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:47,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:31:57,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:07,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:17,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:28,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:38,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:48,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:32:58,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:08,601 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:18,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:28,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:39,032 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:49,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:33:59,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:34:09,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:34:19,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:34:29,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:34:39,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:34:50,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:00,259 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:10,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:20,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:30,708 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:40,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:35:50,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:01,134 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:11,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:21,415 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:31,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:41,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:36:51,865 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:02,007 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:12,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:22,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:32,449 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:42,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:37:52,781 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:02,912 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:13,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:23,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:33,314 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:43,449 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:38:53,601 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:03,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:13,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:24,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:34,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:44,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:39:54,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:04,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:15,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:25,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:35,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:45,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:40:55,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:05,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:16,014 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:26,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:36,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:46,426 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:41:56,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:06,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:16,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:27,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:37,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:47,438 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:42:57,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:07,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:17,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:27,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:38,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:48,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:43:58,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:08,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:18,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:28,799 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:38,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:49,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:44:59,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:45:09,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:45:19,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:45:29,671 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:45:39,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:45:49,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:00,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:10,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:20,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:30,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:41,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:46:51,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:01,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:11,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:21,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:31,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:41,976 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:47:52,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:02,252 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:12,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:22,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:32,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:42,890 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:48:53,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:03,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:13,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:23,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:33,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:43,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:49:53,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:04,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:14,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:24,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:34,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:44,656 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:50:54,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:04,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:15,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:25,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:35,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:45,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:51:55,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:05,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:15,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:26,075 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:36,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:46,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:52:56,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:06,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:16,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:27,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:37,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:47,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:53:57,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:07,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:17,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:27,976 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:38,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:48,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:54:58,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:08,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:18,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:28,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:38,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:49,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:55:59,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:56:09,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:56:19,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:56:29,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:56:39,830 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:56:49,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:00,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:10,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:20,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:30,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:40,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:57:50,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:00,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:11,117 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:21,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:31,409 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:41,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:58:51,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:01,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:11,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:22,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:32,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:42,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 11:59:52,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:02,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:12,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:22,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:33,328 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:43,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:00:53,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:03,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:13,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:24,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:34,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:44,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:01:54,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:04,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:14,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:24,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:35,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:45,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:02:55,386 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:05,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:16,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:26,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:36,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:46,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:03:56,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:06,756 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:16,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:27,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:37,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:47,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:04:57,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:07,570 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:17,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:27,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:37,980 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:48,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:05:58,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:08,379 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:18,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:28,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:38,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:48,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:06:59,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:09,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:19,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:29,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:39,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:49,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:07:59,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:08:09,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:08:20,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:08:30,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:08:40,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:08:50,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:00,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:10,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:20,992 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:31,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:41,252 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:09:51,441 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:01,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:11,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:21,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:32,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:42,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:10:52,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:02,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:12,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:23,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:33,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:43,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:11:53,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:03,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:13,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:23,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:34,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:44,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:12:54,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:04,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:14,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:24,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:34,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:45,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:13:55,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:05,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:15,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:25,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:35,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:45,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:14:56,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:06,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:16,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:26,409 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:36,544 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:46,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:15:56,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:06,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:17,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:27,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:37,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:47,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:16:57,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:07,715 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:17,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:27,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:38,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:48,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:17:58,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:08,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:18,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:28,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:38,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:49,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:18:59,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:19:09,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:19:19,480 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:19:29,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:19:39,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:19:49,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:00,397 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:10,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:20,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:30,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:40,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:20:51,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:01,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:11,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:21,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:31,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:41,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:21:51,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:02,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:12,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:22,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:32,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:42,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:22:52,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:02,799 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:12,931 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:23,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:33,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:43,328 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:23:53,460 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:03,593 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:13,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:23,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:33,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:44,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:24:54,256 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:04,389 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:14,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:24,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:34,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:44,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:25:55,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:05,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:15,410 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:25,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:35,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:45,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:26:55,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:06,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:16,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:26,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:36,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:46,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:27:56,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:06,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:17,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:27,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:37,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:47,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:28:57,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:07,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:17,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:28,052 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:38,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:48,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:29:58,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:08,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:18,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:28,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:39,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:49,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:30:59,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:31:09,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:31:19,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:31:29,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:31:39,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:31:50,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:00,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:10,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:20,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:30,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:40,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:32:50,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:01,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:11,213 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:21,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:31,494 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:41,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:33:51,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:01,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:12,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:22,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:32,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:42,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:34:52,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:03,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:13,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:23,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:33,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:43,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:35:53,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:03,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:14,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:24,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:34,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:44,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:36:55,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:05,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:15,332 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:25,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:35,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:46,182 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:37:56,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:06,950 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:17,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:27,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:37,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:48,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:38:59,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:09,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:21,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:31,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:41,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:52,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:58,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:39:59,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 12:40:08,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:40:18,687 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:40:28,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:40:38,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:40:49,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:00,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:10,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:22,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:32,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:42,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:41:53,096 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:03,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:13,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:23,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:33,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:43,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:42:54,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:04,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:14,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:24,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:34,601 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:44,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:43:54,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:05,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:15,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:25,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:35,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:45,531 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:44:55,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:05,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:16,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:26,143 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:36,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:46,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:45:56,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:06,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:16,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:26,943 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:37,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:47,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:46:57,340 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:07,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:17,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:27,756 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:37,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:48,033 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:47:58,172 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:08,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:18,435 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:28,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:38,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:48,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:48:58,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:09,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:19,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:29,423 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:39,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:49,729 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:49:59,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:50:09,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:50:20,128 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:50:30,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:50:40,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:50:50,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:00,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:10,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:20,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:31,072 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:41,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:51:51,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:01,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:11,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:21,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:31,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:42,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:52:52,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:02,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:12,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:22,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:32,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:42,828 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:53:53,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:03,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:13,606 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:23,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:33,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:44,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:54:54,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:04,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:14,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:24,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:34,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:44,808 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:55:55,039 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:05,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:15,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:25,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:35,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:45,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:56:55,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:05,968 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:16,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:26,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:36,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:46,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:57:56,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:06,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:16,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:27,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:37,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:47,320 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:58:57,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:07,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:17,708 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:27,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:37,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:48,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 12:59:58,225 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:08,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:18,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:28,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:38,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:48,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:00:59,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:09,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:19,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:29,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:39,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:49,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:01:59,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:02:09,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:02:20,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:02:30,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:02:40,379 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:02:50,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:00,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:10,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:21,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:31,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:41,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:03:51,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:01,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:11,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:21,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:32,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:42,153 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:04:52,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:02,412 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:12,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:22,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:32,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:42,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:05:53,065 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:03,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:13,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:23,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:33,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:43,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:06:53,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:03,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:14,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:24,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:34,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:44,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:07:54,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:04,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:15,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:25,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:35,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:45,425 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:08:55,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:05,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:15,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:25,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:36,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:46,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:09:56,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:06,475 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:16,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:26,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:36,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:47,422 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:10:57,554 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:07,693 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:17,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:27,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:38,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:48,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:11:58,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:08,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:18,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:28,781 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:38,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:49,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:12:59,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:13:09,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:13:19,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:13:29,660 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:13:39,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:13:49,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:00,067 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:10,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:20,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:30,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:40,641 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:14:50,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:00,912 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:11,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:21,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:31,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:41,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:15:51,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:01,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:11,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:21,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:32,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:42,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:16:52,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:02,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:12,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:22,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:32,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:43,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:17:53,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:03,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:13,531 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:23,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:33,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:44,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:18:54,134 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:04,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:14,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:24,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:34,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:44,799 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:19:55,011 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:05,143 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:15,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:25,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:35,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:45,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:20:55,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:05,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:16,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:26,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:36,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:46,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:21:56,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:06,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:16,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:27,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:37,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:47,397 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:22:57,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:07,660 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:17,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:27,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:38,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:48,188 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:23:58,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:08,506 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:18,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:28,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:38,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:49,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:24:59,199 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:25:09,332 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:25:19,476 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:25:29,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:25:39,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:25:50,030 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:00,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:10,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:20,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:30,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:40,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:26:51,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:01,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:11,328 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:21,461 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:31,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:42,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:27:52,298 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:02,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:12,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:22,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:32,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:43,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:28:53,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:03,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:13,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:23,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:33,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:43,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:29:54,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:04,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:14,559 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:24,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:34,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:45,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:30:55,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:05,300 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:15,441 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:25,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:35,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:45,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:31:56,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:06,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:16,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:26,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:36,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:46,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:32:56,903 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:07,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:17,198 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:27,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:37,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:47,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:33:57,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:07,931 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:18,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:28,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:38,410 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:48,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:34:58,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:08,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:19,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:29,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:39,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:49,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:35:59,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:36:09,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:36:19,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:36:30,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:36:40,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:36:50,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:00,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:10,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:20,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:31,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:41,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:37:51,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:01,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:11,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:21,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:31,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:42,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:38:52,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:02,443 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:12,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:22,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:32,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:43,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:39:53,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:03,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:13,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:23,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:33,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:43,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:40:54,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:04,229 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:14,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:24,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:34,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:44,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:41:55,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:05,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:15,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:25,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:35,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:45,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:42:55,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:06,057 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:16,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:26,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:36,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:46,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:43:56,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:07,007 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:17,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:27,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:37,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:47,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:44:58,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:08,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:18,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:28,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:38,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:48,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:45:58,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:09,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:19,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:29,436 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:39,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:49,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:46:59,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:47:09,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:47:20,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:47:30,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:47:40,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:47:50,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:00,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:10,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:21,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:31,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:41,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:48:51,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:01,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:11,816 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:21,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:32,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:42,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:49:52,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:02,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:12,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:22,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:32,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:43,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:50:53,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:03,417 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:13,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:24,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:34,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:44,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:51:54,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:04,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:14,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:24,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:35,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:45,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:52:55,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:05,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:15,647 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:25,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:35,943 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:46,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:53:56,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:06,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:16,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:26,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:36,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:46,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:54:57,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:07,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:17,354 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:27,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:37,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:47,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:55:57,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:08,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:18,213 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:28,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:38,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:48,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:56:58,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:09,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:19,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:29,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:39,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:49,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:57:59,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:58:09,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:58:20,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:58:30,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:58:40,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:58:50,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:00,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:10,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:20,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:31,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:41,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 13:59:51,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:01,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:11,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:22,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:32,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:42,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:00:52,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:02,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:12,824 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:22,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:33,485 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:43,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:01:53,814 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:03,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:14,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:24,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:34,352 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:44,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:02:54,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:04,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:15,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:25,249 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:35,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:45,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:03:55,651 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:05,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:15,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:26,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:36,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:46,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:04:56,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:06,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:16,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:27,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:37,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:47,277 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:05:57,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:07,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:17,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:27,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:37,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:48,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:06:58,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:08,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:18,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:28,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:38,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:49,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:07:59,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:09,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:19,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:29,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:39,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:49,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:08:59,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:09:10,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:09:20,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:09:30,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:09:40,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:09:50,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:00,888 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:11,026 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:21,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:31,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:41,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:10:51,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:01,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:11,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:22,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:32,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:42,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:11:52,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:02,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:12,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:23,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:33,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:43,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:12:53,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:04,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:14,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:24,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:34,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:44,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:13:54,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:05,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:15,198 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:25,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:35,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:45,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:14:55,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:05,983 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:16,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:26,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:36,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:46,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:15:56,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:06,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:17,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:27,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:37,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:47,504 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:16:57,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:07,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:17,912 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:28,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:38,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:48,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:17:58,449 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:08,594 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:18,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:29,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:39,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:49,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:18:59,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:19:09,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:19:20,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:19:30,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:19:40,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:19:50,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:00,683 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:10,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:20,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:31,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:41,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:20:51,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:01,608 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:11,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:21,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:32,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:42,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:21:52,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:02,496 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:12,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:22,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:32,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:43,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:22:53,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:03,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:13,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:23,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:33,835 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:43,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:23:54,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:04,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:14,401 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:24,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:34,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:44,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:24:54,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:05,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:15,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:25,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:35,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:45,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:25:55,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:06,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:16,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:26,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:36,424 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:46,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:26:56,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:06,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:16,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:27,182 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:37,326 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:47,462 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:27:57,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:07,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:17,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:28,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:38,153 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:48,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:28:58,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:08,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:18,800 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:28,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:39,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:49,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:29:59,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:30:09,496 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:30:19,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:30:29,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:30:39,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:30:50,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:00,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:10,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:20,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:30,760 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:40,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:31:51,033 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:32:01,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:32:11,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:32:21,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 14:32:31,608 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:10,791 - __main__ - INFO - Bot başlatılıyor...
2025-02-08 23:09:11,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getMe "HTTP/1.1 200 OK"
2025-02-08 23:09:12,559 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteWebhook "HTTP/1.1 200 OK"
2025-02-08 23:09:12,561 - telegram.ext.Application - INFO - Application started
2025-02-08 23:09:13,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:14,326 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:09:14,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteMessage "HTTP/1.1 200 OK"
2025-02-08 23:09:14,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 400 Bad Request"
2025-02-08 23:09:14,910 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 244, in content_filter
    await update.message.reply_text("⏳ Lütfen mesajlarınızı yavaş gönderin!")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 325, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Message to be replied not found
2025-02-08 23:09:24,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:27,713 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:29,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:09:34,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:36,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:46,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:09:57,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:00,180 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:01,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:10:07,170 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:17,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:27,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:37,658 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:47,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:10:57,981 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:08,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:14,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:15,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:11:20,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:30,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:33,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:33,216 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 52, in wrapper
    return await func(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 126, in mute
    permissions=ChatPermissions(
                ^^^^^^^^^^^^^^^^
TypeError: ChatPermissions.__init__() got an unexpected keyword argument 'can_send_media_messages'
2025-02-08 23:11:43,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:11:53,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:03,692 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:13,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:23,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:33,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:43,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:12:53,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:04,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:14,298 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:24,462 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:25,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:32,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:33,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:13:35,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:35,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:13:40,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:40,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getChatMemberCount "HTTP/1.1 200 OK"
2025-02-08 23:13:40,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:13:44,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:50,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:13:50,212 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 52, in wrapper
    return await func(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 120, in mute
    user = update.message.reply_to_message.from_user
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'from_user'
2025-02-08 23:14:00,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:02,366 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:02,368 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 52, in wrapper
    return await func(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 126, in mute
    permissions=ChatPermissions(
                ^^^^^^^^^^^^^^^^
TypeError: ChatPermissions.__init__() got an unexpected keyword argument 'can_send_media_messages'
2025-02-08 23:14:09,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:10,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:14:19,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:19,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:21,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:28,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:28,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/banChatMember "HTTP/1.1 400 Bad Request"
2025-02-08 23:14:28,918 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 52, in wrapper
    return await func(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 167, in ban
    await update.message.chat.ban_member(user.id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_chat.py", line 730, in ban_member
    return await self.get_bot().ban_chat_member(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 928, in ban_chat_member
    return await super().ban_chat_member(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 2991, in ban_chat_member
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 325, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: User is an administrator of the chat
2025-02-08 23:14:38,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:40,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:51,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:14:58,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:00,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:10,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:21,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:31,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:41,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:15:51,858 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:02,035 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:07,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:08,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:08,798 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/deleteMessage "HTTP/1.1 200 OK"
2025-02-08 23:16:08,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 400 Bad Request"
2025-02-08 23:16:08,959 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 244, in content_filter
    await update.message.reply_text("⏳ Lütfen mesajlarınızı yavaş gönderin!")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 325, in _request_wrapper
    raise BadRequest(message)
telegram.error.BadRequest: Message to be replied not found
2025-02-08 23:16:10,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:14,384 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:24,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:34,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:42,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:47,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:16:57,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:07,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:10,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:21,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:31,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:41,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:17:51,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:02,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:12,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:19,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:22,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:26,916 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\streams\tls.py", line 140, in _call_sslobject_method
    result = func(*args)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 979, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLWantReadError: The operation did not complete (read) (_ssl.c:1006)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_tasks.py", line 115, in fail_after
    yield cancel_scope
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 69, in start_tls
    ssl_stream = await anyio.streams.tls.TLSStream.wrap(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\streams\tls.py", line 132, in wrap
    await wrapper._call_sslobject_method(ssl_object.do_handshake)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\streams\tls.py", line 147, in _call_sslobject_method
    data = await self.transport_stream.receive()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 1246, in receive
    await self._protocol.read_event.wait()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\locks.py", line 213, in wait
    await fut
asyncio.exceptions.CancelledError: Cancelled by cancel scope 2225dff21d0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 78, in start_tls
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 68, in start_tls
    with anyio.fail_after(timeout):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_tasks.py", line 118, in fail_after
    raise TimeoutError
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 149, in _connect
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 66, in start_tls
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_application.py", line 1184, in process_update
    await coroutine
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_basehandler.py", line 141, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\kodlama\Group Help Bot\bot.py", line 57, in start
    await update.message.reply_text(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_message.py", line 1092, in reply_text
    return await self.get_bot().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 2613, in send_message
    return await super().send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 820, in send_message
    return await self._send_message(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 517, in _send_message
    result = await super()._send_message(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 572, in _send_message
    result = await self._post(
             ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 236, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out
2025-02-08 23:18:32,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:42,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:18:52,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:02,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:12,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:21,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:31,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:38,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:48,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:19:58,729 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:01,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:11,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:11,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:13,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:14,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:20:18,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:20,842 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:22,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:20:29,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:29,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:20:39,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:45,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:20:46,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:20:56,350 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:06,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:16,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:26,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:31,981 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:34,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:21:41,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:21:51,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:01,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:06,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:06,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:22:16,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:20,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:23,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:24,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:25,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:22:32,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:40,765 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:41,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:22:41,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:44,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:49,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:22:50,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/sendMessage "HTTP/1.1 200 OK"
2025-02-08 23:22:51,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:01,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:12,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:22,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:23,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:29,601 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:35,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:42,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:50,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:23:55,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:01,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:06,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:12,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:12,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:20,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7915145533:AAEDzP0Imuw1v63bumUFANXB9nCLbtZhMBU/getUpdates "HTTP/1.1 200 OK"
2025-02-08 23:24:44,069 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:24:44,070 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:24:56,332 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:24:56,332 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:08,590 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:08,590 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:20,833 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:20,834 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:33,091 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:25:33,092 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:00,354 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:00,354 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:12,596 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:12,596 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:24,854 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:24,854 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:52,100 - telegram.ext.Updater - ERROR - Error while getting Updates: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:26:52,100 - __main__ - ERROR - Exception while handling an update:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 10, in map_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    stream: anyio.abc.ByteStream = await anyio.connect_tcp(
                                   ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 208, in connect_tcp
    gai_res = await getaddrinfo(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_core\_sockets.py", line 581, in getaddrinfo
    gai_res = await get_async_backend().getaddrinfo(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 2683, in getaddrinfo
    return await get_running_loop().getaddrinfo(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 60, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 353, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 262, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection_pool.py", line 245, in handle_async_request
    response = await connection.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 92, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 69, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_async\connection.py", line 117, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_backends\anyio.py", line 112, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 219, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1617, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1645, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1682, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1719, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 352, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_transports\default.py", line 77, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 628, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 333, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_updater.py", line 322, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 553, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 394, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 3542, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 482, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\ext\_extbot.py", line 335, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\_bot.py", line 510, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 168, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 288, in _request_wrapper
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_baserequest.py", line 278, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\telegram\request\_httpxrequest.py", line 243, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-02-08 23:27:19,898 - __main__ - INFO - Bot başlatılıyor...
2025-02-08 23:27:41,694 - __main__ - INFO - Bot başlatılıyor...
