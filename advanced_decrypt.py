#!/usr/bin/env python3
import base64
import hashlib

def try_simple_substitution(data):
    """Basit karakter <PERSON>i<PERSON>tirme şifrelerini dene"""
    
    # Vigenère cipher denemesi (yaygın anah<PERSON>larla)
    common_keys = ["TURKMEN", "BAGLAN", "NEWS", "CRYPTO", "SECRET", "PASSWORD"]
    
    for key in common_keys:
        print(f"\n=== Vigenère Anahtar: {key} ===")
        try:
            result = vigenere_decrypt(data, key)
            ascii_count = sum(1 for b in result if 32 <= ord(b) <= 126)
            ascii_ratio = ascii_count / len(result)
            
            print(f"ASCII oranı: {ascii_ratio:.2%}")
            if ascii_ratio > 0.7:
                print(f"Sonuç: {result[:200]}...")
                with open(f"vigenere_{key.lower()}.txt", "w", encoding="utf-8") as f:
                    f.write(result)
        except Exception as e:
            print(f"Hata: {e}")

def vigenere_decrypt(ciphertext, key):
    """Vigenère şifre çözme"""
    result = ""
    key = key.upper()
    key_index = 0
    
    for char in ciphertext:
        if char.isalpha():
            # Anahtar karakterini al
            key_char = key[key_index % len(key)]
            key_shift = ord(key_char) - ord('A')
            
            # Şifre çöz
            if char.isupper():
                decrypted = chr((ord(char) - ord('A') - key_shift) % 26 + ord('A'))
            else:
                decrypted = chr((ord(char) - ord('a') - key_shift) % 26 + ord('a'))
            
            result += decrypted
            key_index += 1
        else:
            result += char
    
    return result

def try_reverse_operations(data):
    """Ters işlemler dene"""
    
    # Ters çevir
    reversed_data = data[::-1]
    print(f"Ters çevrilmiş: {reversed_data[:100]}...")
    
    # Base64 tekrar dene
    try:
        double_decoded = base64.b64decode(data)
        print(f"Çift Base64: {double_decoded[:100]}...")
    except:
        print("Çift Base64 başarısız")

def analyze_patterns(data):
    """Veri desenlerini analiz et"""
    
    print("\n=== DESEN ANALİZİ ===")
    
    # Byte frekansı
    freq = {}
    for byte in data:
        freq[byte] = freq.get(byte, 0) + 1
    
    # En yaygın byte'lar
    sorted_freq = sorted(freq.items(), key=lambda x: x[1], reverse=True)
    print("En yaygın byte'lar:")
    for byte, count in sorted_freq[:10]:
        print(f"  {byte:02x} ({byte}): {count} kez")
    
    # Entropi hesapla
    import math
    entropy = 0
    total = len(data)
    for count in freq.values():
        p = count / total
        if p > 0:
            entropy -= p * math.log2(p)
    
    print(f"Entropi: {entropy:.2f}")
    
    if entropy < 6:
        print("Düşük entropi - basit şifre olabilir")
    elif entropy > 7.5:
        print("Yüksek entropi - güçlü şifreleme veya sıkıştırma")

def main():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    decoded = base64.b64decode(clean_data)
    
    # Desen analizi
    analyze_patterns(decoded)
    
    # String olarak dene
    try:
        text_data = decoded.decode('latin-1')  # Latin-1 tüm byte'ları destekler
        
        # Basit substitution dene
        try_simple_substitution(text_data)
        
        # Ters işlemler
        try_reverse_operations(text_data)
        
    except Exception as e:
        print(f"String dönüşüm hatası: {e}")
    
    # Hex dump
    print("\n=== HEX DUMP (İlk 256 byte) ===")
    for i in range(0, min(256, len(decoded)), 16):
        hex_part = " ".join(f"{b:02x}" for b in decoded[i:i+16])
        ascii_part = "".join(chr(b) if 32 <= b <= 126 else "." for b in decoded[i:i+16])
        print(f"{i:04x}: {hex_part:<48} {ascii_part}")

if __name__ == "__main__":
    main()
