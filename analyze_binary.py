#!/usr/bin/env python3
import base64

def analyze_binary_data():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    decoded = base64.b64decode(clean_data)
    
    print("=== BINARY VERİ ANALİZİ ===")
    print(f"Uzunluk: {len(decoded)} bytes")
    
    # İlk birkaç byte'ı kontrol et
    print(f"İlk 16 byte (hex): {decoded[:16].hex()}")
    print(f"İlk 16 byte (decimal): {[b for b in decoded[:16]]}")
    
    # ASCII karakterleri ara
    ascii_chars = ""
    for byte in decoded:
        if 32 <= byte <= 126:  # Yazdırılabilir ASCII
            ascii_chars += chr(byte)
        else:
            ascii_chars += "."
    
    print(f"ASCII karakterler: {ascii_chars[:100]}...")
    
    # XOR ile çözme denemesi (yaygın anahtarlarla)
    common_keys = [0x42, 0x13, 0x37, 0xFF, 0xAA, 0x55]
    
    for key in common_keys:
        print(f"\n=== XOR Anahtar {key:02x} ===")
        xor_result = bytes([b ^ key for b in decoded])
        
        # ASCII kontrol
        ascii_count = sum(1 for b in xor_result if 32 <= b <= 126)
        ascii_ratio = ascii_count / len(xor_result)
        
        print(f"ASCII oranı: {ascii_ratio:.2%}")
        
        if ascii_ratio > 0.7:  # %70'den fazla ASCII ise
            try:
                text = xor_result.decode('utf-8', errors='ignore')
                print(f"Metin: {text[:200]}...")
                
                # Dosyaya kaydet
                with open(f"xor_result_{key:02x}.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                    
            except:
                print("UTF-8 çözülemedi")
    
    # Tek byte XOR brute force
    print("\n=== BRUTE FORCE XOR ===")
    best_ratio = 0
    best_key = 0
    best_text = ""
    
    for key in range(256):
        xor_result = bytes([b ^ key for b in decoded])
        ascii_count = sum(1 for b in xor_result if 32 <= b <= 126)
        ascii_ratio = ascii_count / len(xor_result)
        
        if ascii_ratio > best_ratio:
            best_ratio = ascii_ratio
            best_key = key
            try:
                best_text = xor_result.decode('utf-8', errors='ignore')
            except:
                best_text = "Çözülemedi"
    
    print(f"En iyi anahtar: {best_key:02x} (ASCII oranı: {best_ratio:.2%})")
    print(f"Sonuç: {best_text[:200]}...")
    
    if best_ratio > 0.5:
        with open("best_xor_result.txt", "w", encoding="utf-8") as f:
            f.write(best_text)
        print("En iyi sonuç dosyaya kaydedildi: best_xor_result.txt")

if __name__ == "__main__":
    analyze_binary_data()
