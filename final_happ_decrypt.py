#!/usr/bin/env python3
import base64
import hashlib
import itertools

def comprehensive_decrypt_attempt():
    encrypted_text = "happ://crypt2/b2L12r42m3A53M1stuFsWwyhK2fgZy3EPjp6Zd1K4XOxPFmR7h5Rgv1J+cSp/ejzNx0D/TFaaUMN82GoFtXloa/4c0ocBtH69rRcZTZEvdG+NvcQFG1IkvtaBwoi6ioFHV0A98FFd/tus9nYsvNZo3ti+g6RDmoJ1Qd+vK1vsJZgF7U+i8rvS7VBZ9nfpWg97qnmvYdN+atPUKaCvMxng641iwG+InbNXs/iCCjDH9jhJcQbzOrCuEtdzJ1iPo6tm3OhdMtwBEk8rt6E0WgOwb8lDG7dZoG+/YmJcmHs6NC7Q/mixV5BocE8/IJPc+Y0nAHvTAJoQd98Vz++NXiTu8fxrZHCPsmoAagOaWj7zpLeQ9y46p80AzDn0nAHDjod2Tupgqmzov6FbQUr7dlu5LUUSMv0VaLWzLdP7HPkeP8T+HofELNh1JiOwCny65ai5fpPZ42erWtG1mmMprwnwvluDNoAMAPvar4iccC4FM8Yi4Qq/DNb7q/DH0xptKVnEgXME/+8RWp0oWzJdyYAi5QZ+Etmi+/ofiMVYUAmmuj9mKhWUthTUwCIVEsnXNFD5Nx5sjlmgyJK0cDm60XPZ8TPRPv1pqvC4v0ovrMH/DXZHejQEBtU5AiSn9oPmXTheHZ2A2HkTJt5YwqlhE/vVEfrB3gAkq4g269TUgQnzIQ="
    
    # Base64 çöz
    clean_data = encrypted_text.split("happ://crypt2/")[1]
    encrypted_data = base64.b64decode(clean_data)
    
    print("KAPSAMLI HAPP VPN ŞİFRE ÇÖZME DENEMESİ")
    print("=" * 60)
    
    # Daha fazla anahtar kombinasyonu
    key_candidates = [
        # Basit stringler
        "happ", "HAPP", "Happ",
        "happvpn", "HAPPVPN", "HappVPN", "Happ_VPN",
        "crypt2", "CRYPT2", "Crypt2",
        "happcrypt2", "HAPPCRYPT2", "HappCrypt2",
        
        # VPN terimleri
        "vpn", "VPN", "openvpn", "OPENVPN",
        "config", "CONFIG", "vpnconfig", "VPNCONFIG",
        
        # Yaygın şifreler
        "password", "PASSWORD", "secret", "SECRET",
        "key", "KEY", "private", "PRIVATE",
        "admin", "ADMIN", "user", "USER",
        
        # Türkçe
        "sifre", "SIFRE", "anahtar", "ANAHTAR",
        "gizli", "GIZLI", "parola", "PAROLA",
    ]
    
    best_results = []
    
    print(f"Toplam {len(key_candidates)} anahtar deneniyor...")
    
    for i, key_str in enumerate(key_candidates):
        # String olarak
        key_bytes = key_str.encode('utf-8')
        
        # Hash'li versiyonlar
        hash_variants = [
            key_bytes,
            hashlib.md5(key_bytes).digest(),
            hashlib.sha1(key_bytes).digest(),
            hashlib.sha256(key_bytes).digest(),
            hashlib.md5(key_bytes).digest()[:16],
            hashlib.sha1(key_bytes).digest()[:16],
            hashlib.sha256(key_bytes).digest()[:16],
            hashlib.sha256(key_bytes).digest()[:32],
        ]
        
        for j, key in enumerate(hash_variants):
            try:
                # XOR ile çöz
                result = bytes([encrypted_data[k] ^ key[k % len(key)] for k in range(len(encrypted_data))])
                
                # Kalite kontrolü
                ascii_count = sum(1 for b in result if 32 <= b <= 126)
                ascii_ratio = ascii_count / len(result)
                
                # VPN anahtar kelimelerini ara
                try:
                    text = result.decode('utf-8', errors='ignore')
                    vpn_score = 0
                    vpn_keywords = [
                        'client', 'remote', 'port', 'proto', 'dev', 'tun', 'tap',
                        'ca', 'cert', 'key', 'dh', 'cipher', 'auth', 'comp-lzo',
                        'verb', 'mute', 'resolv-retry', 'nobind', 'persist-key',
                        'persist-tun', 'redirect-gateway', 'dhcp-option',
                        'BEGIN CERTIFICATE', 'END CERTIFICATE',
                        'BEGIN PRIVATE KEY', 'END PRIVATE KEY',
                        'BEGIN RSA PRIVATE KEY', 'END RSA PRIVATE KEY',
                        # JSON anahtar kelimeleri
                        '"server"', '"port"', '"password"', '"method"',
                        '"protocol"', '"obfs"', '"remarks"',
                        # V2Ray/VMess
                        '"v":', '"ps":', '"add":', '"port":', '"id":', '"aid":',
                        '"net":', '"type":', '"host":', '"path":', '"tls":',
                        # Shadowsocks
                        'ss://', 'ssr://', 'vmess://', 'trojan://',
                    ]
                    
                    text_lower = text.lower()
                    for keyword in vpn_keywords:
                        if keyword.lower() in text_lower:
                            vpn_score += 1
                    
                    # Toplam skor
                    total_score = ascii_ratio * 100 + vpn_score * 10
                    
                    if ascii_ratio > 0.4 or vpn_score > 0:
                        best_results.append({
                            'key_str': key_str,
                            'key_type': j,
                            'key_hex': key.hex(),
                            'ascii_ratio': ascii_ratio,
                            'vpn_score': vpn_score,
                            'total_score': total_score,
                            'text': text,
                            'result': result
                        })
                        
                except:
                    pass
                    
            except Exception as e:
                continue
    
    # En iyi sonuçları sırala
    best_results.sort(key=lambda x: x['total_score'], reverse=True)
    
    print(f"\nToplam {len(best_results)} umut verici sonuç bulundu!")
    print("\nEN İYİ 10 SONUÇ:")
    print("-" * 80)
    
    for i, result in enumerate(best_results[:10]):
        print(f"\n{i+1}. SONUÇ:")
        print(f"   Anahtar: {result['key_str']}")
        print(f"   Tip: {result['key_type']} (0=raw, 1=md5, 2=sha1, 3=sha256, 4=md5[:16], 5=sha1[:16], 6=sha256[:16], 7=sha256[:32])")
        print(f"   Hex: {result['key_hex'][:32]}...")
        print(f"   ASCII: {result['ascii_ratio']:.2%}")
        print(f"   VPN Score: {result['vpn_score']}")
        print(f"   Total Score: {result['total_score']:.2f}")
        print(f"   Metin: {result['text'][:100]}...")
        
        # En iyi sonucu dosyaya kaydet
        if i == 0:
            with open("best_happ_result.txt", "w", encoding="utf-8") as f:
                f.write(f"Anahtar: {result['key_str']}\n")
                f.write(f"Tip: {result['key_type']}\n")
                f.write(f"Hex: {result['key_hex']}\n")
                f.write(f"ASCII Oranı: {result['ascii_ratio']:.2%}\n")
                f.write(f"VPN Score: {result['vpn_score']}\n")
                f.write(f"Total Score: {result['total_score']:.2f}\n")
                f.write("-" * 50 + "\n")
                f.write(result['text'])
            
            print(f"\n   *** EN İYİ SONUÇ DOSYAYA KAYDEDİLDİ: best_happ_result.txt ***")
            
            # VPN config olarak da kaydet
            if result['vpn_score'] > 0:
                with open("best_happ_config.ovpn", "w", encoding="utf-8") as f:
                    f.write(result['text'])
                print(f"   *** VPN CONFIG OLARAK KAYDEDİLDİ: best_happ_config.ovpn ***")

if __name__ == "__main__":
    comprehensive_decrypt_attempt()
